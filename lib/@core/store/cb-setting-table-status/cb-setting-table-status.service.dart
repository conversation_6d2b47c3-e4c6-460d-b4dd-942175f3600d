// ignore_for_file: inference_failure_on_collection_literal, unused_local_variable

part of 'cb-setting-table-status.cubit.dart';

class StaffAddService {

  // Future<Staff?> onSaveTableStatus(Map<String, dynamic> body) async {
  //   try {
  //     // print('Param: $isEdit - $uid - $param');
  //     // final response = await AppHttp.request(
  //     //   isEdit ? '${AppAPI.userStaffs}/$uid' : AppAPI.userStaffs,
  //     //   method: isEdit ? APIRequestMethod.PATCH : APIRequestMethod.POST,
  //     //   body: param,
  //     // );
  //     // AppBased.doAuditLogging(
  //     //   pageName: 'StaffAddPage',
  //     //   pageUrl: 'staff-add',
  //     //   pageAction: 'addOrEditStaff',
  //     //   metadata: param,
  //     // );

  //     return Staff.fromJson(response.data as Map<String, dynamic>);
  //   } catch (e) {
  //     AppError.dioResponseError(e);
  //   }
  //   return null;
  // }

}
