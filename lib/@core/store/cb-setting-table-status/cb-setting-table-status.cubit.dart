// ignore_for_file: avoid_catching_errors, avoid_positional_boolean_parameters

import 'package:bloc/bloc.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:stickyqrbusiness/@common/common.dart';
import 'package:stickyqrbusiness/@core/core.dart';
import 'package:stickyqrbusiness/@utils/utils.dart';

part 'cb-setting-table-status.state.dart';
part 'cb-setting-table-status.service.dart';

class CBSettingTableStatusCubit extends Cubit<CBSettingTableStatusState> {
  CBSettingTableStatusCubit()
      : super(
          CBSettingTableStatusState()
        );

  void onResetStatus() {
    emit(state.copyWith(status: CBSettingTableStatus.Initial, errorMsg: ''));
  }

  void onEnableInProgress(bool value) {
    emit(
      state.copyWith(
        inProgress: value,
        status: CBSettingTableStatus.Edit,
      ),
    );
  }

  void onEnableAttentionRequired(bool value) {
    emit(
      state.copyWith(
        attentionRequired: value,
        status: CBSettingTableStatus.Edit,
      ),
    );
  }

  void onChangedInProgressTime(String value) {
    emit(
      state.copyWith(
        inProgressTime: value,
        status: CBSettingTableStatus.Edit,
      ),
    );
  }

  void onChangedAttentionRequiredTime(String value) {
    emit(
      state.copyWith(
        attentionRequiredTime: value,
        status: CBSettingTableStatus.Edit,
      ),
    );
  }


  Future<Staff?> onSave({bool isEdit = false, List<String?>? tagIds}) async {
    emit(state.copyWith(status: CBSettingTableStatus.Loading));
    try {
      // final param = {
      //   'displayName': state.staffName.value,
      //   'phone': '+${state.currentCode}${state.phoneNumber.value}',
      //   'initialPassword': state.password?.value ?? '',
      // };
      // AppLog.e('param: $param');
      // final rep = await StaffAddService().addStaff(
      //   isEdit: isEdit,
      //   name: state.staffName.value,
      //   phone: '+${state.currentCode}${state.phoneNumber.value}',
      //   // pass: state.password?.value ?? '',
      //   uid: state.uid,
      //   tags: tagIds,
      //   role: state.role,
      // );
      // if (rep != null) {
      //   emit(
      //     state.copyWith(
      //       status: CBSettingTableStatus.Success,
      //       staff: rep,
      //     ),
      //   );
      // } else {
      //   emit(state.copyWith(status: CBSettingTableStatus.Error, staff: null));
      // }
      // return rep;
    } on AppErrorResponse catch (e) {
      AppLog.e(
        'Error: ${e.message}',
        logType: AppLoggerType.FUNCTION,
        logValue: 'login',
        classParent: 'CBSettingTableStatusCubit',
        obj: e,
      );
      emit(state.copyWith(
          status: CBSettingTableStatus.Error, errorMsg: e.message));
      return null;
    }
    return null;
  }

}
