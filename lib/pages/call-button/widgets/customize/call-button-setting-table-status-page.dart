import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:stickyqrbusiness/@const/colors.dart';
import 'package:stickyqrbusiness/@core/store/cb-setting-table-status/cb-setting-table-status.cubit.dart';
import 'package:stickyqrbusiness/@widgets/button-loading-widget.dart';
import 'package:stickyqrbusiness/l10n/l10n.dart';

class CBSettingTableStatusPage extends StatelessWidget {
  const CBSettingTableStatusPage({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => CBSettingTableStatusCubit(),
      child: const CBSettingTableStatusView(),
    );
  }
}

class CBSettingTableStatusView extends StatefulWidget {
  const CBSettingTableStatusView({super.key});

  @override
  State<CBSettingTableStatusView> createState() => _CBSettingTableStatusState();
}

class _CBSettingTableStatusState extends State<CBSettingTableStatusView>
    with WidgetsBindingObserver {
  late final l10n = context.l10n;

  final List<String> durations = [
    '15 min',
    '30 min+',
    '1 hour',
    '2 hours',
    '4 hours',
    'All day',
  ];
  // bool isInProgress = true;
  // String selectedDuration = '30 min+';

  List<int> get allDurations {
    final List<int> durations = [];
    for (int i = 5; i <= 120; i += 5) {
      durations.add(i);
    }
    return durations;
  }
  
  // Filter duration cho In Progress (tất cả duration)
  List<int> get inProgressDurations => allDurations;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    WidgetsBinding.instance.addPostFrameCallback((_) => _buildCompleted());
  }

  @override
  void dispose() {
    super.dispose();
    WidgetsBinding.instance.removeObserver(this);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _buildAppbar(),
      body: SafeArea(
        child: _buildBody(),
      ),
    );
  }

  AppBar _buildAppbar() {
    return AppBar(
      automaticallyImplyLeading: false,
      centerTitle: true,
      titleSpacing: 0.0,
      backgroundColor: AppColors.lightPrimaryBackgroundColor,
      elevation: 0,
      leading: Padding(
        padding: const EdgeInsets.all(10.0),
        child: SizedBox(
          width: 40,
          child: MaterialButton(
            elevation: 0,
            highlightElevation: 0,
            hoverElevation: 0,
            hoverColor: AppColors.lightPrimaryBackgroundColor,
            onPressed: () => {
              Navigator.of(context).pop(),
            },
            color: AppColors.lightPrimaryBackgroundColor,
            padding: EdgeInsets.zero,
            shape: const CircleBorder(),
            child: SvgPicture.asset(
              'assets/svgs/arrow-back.svg',
            ),
          ),
        ),
      ),
      title: Text(
        l10n.tableStatus,
        style: const TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.w600,
          // color: AppColors.darkPrimaryBackgroundColor,
        ),
      ),
    );
  }

  Widget _buildBody() {
    return Column(
      children: [
        Expanded(child: _buildContent()),
        _buildActionsBtns(),
      ],
    );
  }

  Widget _buildContent() {
    return BlocBuilder<CBSettingTableStatusCubit, CBSettingTableStatusState>(
      builder: (context, state) {
        final inProgress = state.inProgress;
        final attentionRequired = state.attentionRequired;
        final inProgressTime = state.inProgressTime;
        final attentionRequiredTime = state.attentionRequiredTime;

        return Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              _buildTitle(),
              _buildItemSelected(
                isDefault: true,
                isShowChangeAfter: false,
                title: l10n.justSeated,
                isChecked: true,
                color: const Color(0xFFE7EFFB),
                borderColor: const Color(0xFF6777F8),
              ),
              _buildDivider(),
              _buildItemSelected(
                isShowChangeAfter: true,
                title: l10n.inProgressOrder,
                isChecked: inProgress,
                color: const Color(0xFFFFF1D4),
                borderColor: const Color(0xFFB3804A),
                onChecked: (p0) => context
                    .read<CBSettingTableStatusCubit>()
                    .onEnableInProgress(p0),
                durations: inProgressDurations,
                selectedDuration: inProgressTime,
                onChangeAfterFunc: (value) => context
                    .read<CBSettingTableStatusCubit>()
                    .onChangedInProgressTime(value),
              ),
              _buildDivider(),
              _buildItemSelected(
                isShowChangeAfter: true,
                title: l10n.attentionRequired,
                isChecked: attentionRequired,
                color: const Color(0xFFFFE4E7),
                borderColor: const Color(0xFFD33030),
                onChecked: (p0) => context
                    .read<CBSettingTableStatusCubit>()
                    .onEnableAttentionRequired(p0),
                durations: inProgressDurations,
                selectedDuration: attentionRequiredTime,
                onChangeAfterFunc: (value) => context
                    .read<CBSettingTableStatusCubit>()
                    .onChangedInProgressTime(value),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildTitle() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          l10n.enableTableStatus,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          l10n.allowsYouToSetTablesToAutomaticallyChangeColorBasedOnHowLongGuestsHaveBeenSeated,
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.normal,
            color: Colors.black54,
          ),
        ),
      ],
    );
  }

  Widget _buildItemSelected({
    required String title,
    required bool isChecked,
    required Color color,
    required Color borderColor,
    List<String>? durations,
    Function(bool)? onChecked,
    Function(int)? onChangeAfterFunc,
    int? selectedDuration,
    bool isShowChangeAfter = true,
    bool isDefault = false,
  }) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Transform.translate(
          offset: const Offset(-12, 0),
          child: Row(
            children: [
              Opacity(
                opacity: isDefault ? .4 : 1,
                child: Transform.scale(
                  scale: 1,
                  child: Checkbox(
                    value: isDefault ? true : isChecked,
                    onChanged: isDefault
                        ? null
                        : (bool? value) {
                            if (onChecked != null && value != null) {
                              onChecked(value);
                            }
                          },
                    checkColor: Colors.white,
                    fillColor: WidgetStateProperty.resolveWith<Color>(
                      (Set<WidgetState> states) {
                        if (states.contains(WidgetState.selected)) {
                          return Colors.black;
                        }
                        return Colors.white;
                      },
                    ),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(4),
                    ),
                    side: BorderSide(
                      color: Colors.grey[400]!,
                      width: 2,
                    ),
                  ),
                ),
              ),
              Text(
                title,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.black,
                ),
              ),
            ],
          ),
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              l10n.statusColor,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.normal,
                color: Color(0xFF595D62),
              ),
            ),
            Container(
              width: 40,
              height: 40,
              margin: const EdgeInsets.only(left: 16),
              decoration: BoxDecoration(
                border: Border.all(
                  width: 1,
                  color: borderColor,
                ),
                color: color,
                borderRadius: const BorderRadius.all(
                  Radius.circular(8),
                ),
              ),
            ),
          ],
        ),
        if (isShowChangeAfter)
          Padding(
            padding: const EdgeInsets.only(top: 8.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  l10n.changeAfter,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.normal,
                    color: Color(0xFF595D62),
                  ),
                ),
                Container(
                  width: MediaQuery.sizeOf(context).width * 0.45,
                  padding: const EdgeInsets.only(left: 16, right: 12),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.grey.shade300),
                  ),
                  child: DropdownButtonHideUnderline(
                    child: DropdownButton<int>(
                      value: selectedDuration,
                      isExpanded: true,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.normal,
                        color: Colors.black,
                      ),
                      icon: Icon(
                        Icons.keyboard_arrow_down,
                        size: 28,
                        color: Colors.grey.shade600,
                      ),
                      onChanged: (int? newValue) {
                        if (newValue != null) {
                          if (onChangeAfterFunc != null) {
                            onChangeAfterFunc(newValue);
                          } else {
                            setState(() {
                              selectedDuration = newValue;
                            });
                          }
                        }
                      },
                      items: durations?.map<DropdownMenuItem<int>>((int? value) {
                        return DropdownMenuItem<int>(
                          value: value ?? ,
                          child: Text('$value ${l10n.mins}'),
                        );
                      }).toList(),
                    ),
                  ),
                ),
              ],
            ),
          ),
      ],
    );
  }

  Widget _buildActionsBtns() {
    return Container(
      padding: EdgeInsets.fromLTRB(16, 12, 16, Platform.isIOS ? 0 : 12),
      decoration: BoxDecoration(
        border: Border(
          top: BorderSide(
            width: 1,
            color: Colors.grey.shade300,
          ),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: ButtonLoading(
              callback: () {},
              height: 48,
              borderRadius: 8,
              buttonBackgroundColor: AppColors.appColor,
              label: l10n.save,
              labelColor: Colors.white,
              fontSize: 16,
              fontWeight: FontWeight.bold,
              // side: border,
              isLoading: false,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDivider() {
    return const Padding(
      padding: EdgeInsets.only(
        top: 24.0,
        bottom: 8.0,
      ),
      child: Divider(
        height: 1,
        color: Color(0xFFEBEBEB),
      ),
    );
  }

  void _buildCompleted() {}
}
