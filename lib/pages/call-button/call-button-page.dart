// ignore_for_file: strict_raw_type, avoid_dynamic_calls, empty_catches

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:stickyqrbusiness/@const/colors.dart';
import 'package:stickyqrbusiness/@const/routes.dart';
import 'package:stickyqrbusiness/@core/core.dart';
import 'package:stickyqrbusiness/@core/store/business-profile/business-profile-edit.cubit.dart';
import 'package:stickyqrbusiness/@core/store/business-profile/business-profile.cubit.dart';
import 'package:stickyqrbusiness/l10n/l10n.dart';
import 'package:stickyqrbusiness/pages/settings/widgets/@setting-widget.dart';

class CallButtonPage extends StatefulWidget {
  const CallButtonPage({super.key});

  @override
  State<CallButtonPage> createState() => _CallButtonPageState();
}

class _CallButtonPageState extends State<CallButtonPage> {
  @override
  Widget build(BuildContext context) {
    context.read<BusinessProfileCubit>().getBusinessProfile();
    final l10n = context.l10n;
    return MultiBlocListener(
      listeners: [
        BlocListener<BusinessProfileEditCubit, BusinessProfileEditState>(
          listener: (context, state) {
            switch (state.status) {
              case EditStatus.error:
                if (state.errMsg != null && state.errMsg != '') {
                  AppBased.toastError(context, title: state.errMsg);
                  context.read<BusinessProfileEditCubit>().onResetStatus();
                }
                break;
              default:
            }
          },
        ),
        BlocListener<BusinessProfileCubit, BusinessProfileState>(
          listener: (context, state) {
            if (state.status == Status.success) {
              context.read<BusinessProfileEditCubit>().onChangeEnableCallButton(state.businessProfile.enableCallButton ?? false);
            }
          },
        ),
      ],
      child: Scaffold(
        appBar: _buildAppbar(l10n),
        body: SafeArea(
          bottom: false,
          child: BlocListener<AuthBloc, AuthState>(
            listener: (context, state) {
              if (!state.checkRole(PermissionBusiness.cb_manager_tables_services.name)) {
                AppBased.goBackAllPage(context);
              }
            },
            child: _buildContent(context, l10n),
          ),
        ),
      ),
    );
  }

  Widget _buildContent(BuildContext context, AppLocalizations l10n) {
    return SingleChildScrollView(
      child: Column(
        children: [
          BlocBuilder<AuthBloc, AuthState>(
            builder: (context, stateAuth) {
              bool isAllow = stateAuth.enableCallButton ?? false;
              return BlocBuilder<BusinessProfileEditCubit, BusinessProfileEditState>(
                builder: (context, state) {
                  isAllow = state.enableCallButton ?? stateAuth.enableCallButton ?? false;
                  final bool isEnableCheckin = stateAuth.business?.callButtonCheckin?.enabled ?? false;
                  final bool callButtonAllowReviewGoogle = stateAuth.business?.callButtonAllowReviewGoogle ?? false;
                  return Padding(
                    padding: const EdgeInsets.only(bottom: 24, top: 16),
                    child: Column(
                      children: [
                        Column(
                          children: [
                            Container(
                              padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
                              alignment: Alignment.topLeft,
                              child: RichText(
                                textAlign: TextAlign.left,
                                text: TextSpan(
                                  // style: const TextStyle(
                                  //   fontSize: 16,
                                  // ),
                                  children: <InlineSpan>[
                                    TextSpan(
                                      text: l10n.callButtonSubTitle,
                                      style: const TextStyle(
                                        color: Colors.black,
                                        fontSize: 18,
                                        fontWeight: FontWeight.bold,
                                        height: 1.4,
                                      ),
                                    ),
                                    // WidgetSpan(
                                    //   alignment: PlaceholderAlignment.middle,
                                    //   child: InkWell(
                                    //     borderRadius: BorderRadius.circular(4),
                                    //     onTap: () {},
                                    //     child: Padding(
                                    //       padding: const EdgeInsets.only(left: 4, right: 4),
                                    //       child: Text(
                                    //         l10n.learnMore,
                                    //         style: const TextStyle(
                                    //           fontSize: 14,
                                    //           fontWeight: FontWeight.w600,
                                    //           color: AppColors.appResendColor,
                                    //         ),
                                    //       ),
                                    //     ),
                                    //   ),
                                    // ),
                                  ],
                                ),
                              ),
                            ),
                            Container(
                              margin: const EdgeInsets.only(right: 16, left: 16, bottom: 0),
                              padding: const EdgeInsets.fromLTRB(16, 8, 8, 8),
                              decoration: BoxDecoration(
                                border: Border.all(
                                  width: 1,
                                  color: const Color(0xFFEBEBEB),
                                ),
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                children: [
                                  Expanded(
                                    child: Text(
                                      isAllow ? l10n.enabledPointClaims : l10n.disabledPointClaims,
                                      style: const TextStyle(
                                        fontSize: 16,
                                        fontWeight: FontWeight.w400,
                                        color: AppColors.darkPrimaryBackgroundColor,
                                      ),
                                    ),
                                  ),
                                  AbsorbPointer(
                                    absorbing: !stateAuth.checkPermissions(PermissionBusiness.cb_manager_tables_services.name, [2, 3]),
                                    child: Transform.scale(
                                      scale: 0.8,
                                      child: CupertinoSwitch(
                                        value: isAllow,
                                        inactiveTrackColor: AppColors.appBGGreyColor,
                                        activeTrackColor: AppColors.cupertinoSwitchColor,
                                        onChanged: (value) {
                                          context.read<BusinessProfileEditCubit>().onChangeEnableCallButton(value).then((value) {
                                            context.read<BusinessProfileEditCubit>().onUpdateBusinessCallButton();
                                          });
                                        },
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                        _buildFeature(
                          l10n,
                          isEnableCheckin: isEnableCheckin,
                          callButtonAllowReviewGoogle: callButtonAllowReviewGoogle,
                        ),
                        if (isAllow) _buildCustomize(l10n),
                      ],
                    ),
                  );
                },
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildFeature(
    AppLocalizations l10n, {
    bool isEnableCheckin = false,
    bool callButtonAllowReviewGoogle = false,
  }) {
    final status = isEnableCheckin ? l10n.enabledCheckin : l10n.disabledCheckin;
    final statusColor = isEnableCheckin ? const Color(0xFF3448F0) : Colors.grey;
    final statusReview = callButtonAllowReviewGoogle ? l10n.enabledCheckin : l10n.disabledCheckin;
    final statusColorReview = callButtonAllowReviewGoogle ? const Color(0xFF3448F0) : Colors.grey;
    return Column(
      children: [
        Container(
          margin: const EdgeInsets.only(top: 24, bottom: 16),
          height: 8,
          color: AppColors.appBGAvatarColor,
        ),
        Container(
          padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
          alignment: Alignment.topLeft,
          child: Text(
            l10n.features,
            textAlign: TextAlign.left,
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: AppColors.darkPrimaryBackgroundColor,
            ),
          ),
        ),
        Column(
          children: [
            _buildItem(
              l10n,
              l10n.customerCheckin,
              l10n.allowCustomerCheckInEarnPointAndMore,
              onTap: () => AppBased.go(context, AppRoutes.callButtonSettingCheckin),
              customWidget: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Flexible(
                              child: Text(
                                l10n.customerCheckin,
                                style: const TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.w600,
                                  color: AppColors.darkPrimaryBackgroundColor,
                                ),
                              ),
                            ),
                            Container(
                              decoration: BoxDecoration(
                                color: statusColor.withValues(alpha: .12),
                                borderRadius: BorderRadius.circular(12),
                              ),
                              padding: const EdgeInsets.symmetric(horizontal: 8),
                              margin: const EdgeInsets.symmetric(horizontal: 8),
                              child: Text(
                                status,
                                style: TextStyle(
                                  color: statusColor,
                                  fontSize: 14,
                                ),
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 8),
                        Text(
                          l10n.allowCustomerCheckInEarnPointAndMore,
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w400,
                            color: AppColors.appBlackColor.withValues(alpha: .56),
                          ),
                        ),
                      ],
                    ),
                  ),
                  Padding(
                    padding: const EdgeInsets.only(left: 4.0),
                    child: SvgPicture.asset(
                      'assets/svgs/arrow-right-menu.svg',
                      colorFilter: const ColorFilter.mode(
                        AppColors.darkPrimaryBackgroundColor,
                        BlendMode.srcIn,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),
            _buildItem(
              l10n,
              l10n.reviewUsOnGoogleTotal,
              l10n.reviewUsOnGoogleSubtotal,
              onTap: () => AppBased.go(context, AppRoutes.callButtonSettingReviewUsOnGooglePage),
              customWidget: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Flexible(
                              child: Text(
                                l10n.reviewUsOnGoogleTotal,
                                style: const TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.w600,
                                  color: AppColors.darkPrimaryBackgroundColor,
                                ),
                              ),
                            ),
                            Container(
                              decoration: BoxDecoration(
                                color: statusColorReview.withValues(alpha: .12),
                                borderRadius: BorderRadius.circular(12),
                              ),
                              padding: const EdgeInsets.symmetric(horizontal: 8),
                              margin: const EdgeInsets.symmetric(horizontal: 8),
                              child: Text(
                                statusReview,
                                style: TextStyle(
                                  color: statusColorReview,
                                  fontSize: 14,
                                ),
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 8),
                        Text(
                          l10n.reviewUsOnGoogleSubtotal,
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w400,
                            color: AppColors.appBlackColor.withValues(alpha: .56),
                          ),
                        ),
                      ],
                    ),
                  ),
                  Padding(
                    padding: const EdgeInsets.only(left: 4.0),
                    child: SvgPicture.asset(
                      'assets/svgs/arrow-right-menu.svg',
                      colorFilter: const ColorFilter.mode(
                        AppColors.darkPrimaryBackgroundColor,
                        BlendMode.srcIn,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildCustomize(AppLocalizations l10n) {
    return Column(
      children: [
        Container(
          margin: const EdgeInsets.only(top: 24, bottom: 16),
          height: 8,
          color: AppColors.appBGAvatarColor,
        ),
        Container(
          padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
          alignment: Alignment.topLeft,
          child: Text(
            l10n.customizeTitle,
            textAlign: TextAlign.left,
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: AppColors.darkPrimaryBackgroundColor,
            ),
          ),
        ),
        Column(
          children: [
            Padding(
              padding: const EdgeInsets.only(bottom: 16.0),
              child: _buildItem(
                l10n,
                l10n.tableStatus,
                l10n.timeAtTableSinceFirstOrderRequest,
                onTap: () => AppBased.go(context, AppRoutes.callButtonSettingTableStatusPage),
              ),
            ),
            Padding(
              padding: const EdgeInsets.only(bottom: 16.0),
              child: _buildItem(
                l10n,
                l10n.soundSettings,
                l10n.soundSettingSub,
                onTap: () => AppBased.go(context, AppRoutes.callButtonSettingSoundPage),
              ),
            ),
            _buildItem(
              l10n,
              l10n.thankYouSettings,
              l10n.thankYouSettingsSub,
              onTap: () => AppBased.go(context, AppRoutes.callButtonSettingThanksPage),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildItem(
    AppLocalizations l10n,
    String title,
    String subTitle, {
    String? icon = '',
    Function? onTap,
    bool isIconArrowRight = true,
    bool isBeta = false,
    Widget? customWidget,
  }) {
    return Container(
      margin: const EdgeInsets.only(right: 16, left: 16),
      child: InkWell(
        borderRadius: BorderRadius.circular(12),
        onTap: () => onTap?.call(),
        child: Container(
          padding: const EdgeInsets.all(16),
          alignment: Alignment.topLeft,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              width: 1,
              color: AppColors.appBorderColor,
            ),
          ),
          child: customWidget ??
              Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        if (icon != '' && icon != null)
                          Container(
                            margin: const EdgeInsets.only(bottom: 16),
                            child: SvgPicture.asset(
                              icon,
                              // width: 24,
                              // height: 24,
                              // colorFilter: const ColorFilter.mode(
                              //   AppColors.darkPrimaryBackgroundColor,
                              //   BlendMode.srcIn,
                              // ),
                            ),
                          ),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: [
                            Text(
                              title,
                              textAlign: TextAlign.start,
                              style: const TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w600,
                                color: AppColors.darkPrimaryBackgroundColor,
                              ),
                            ),
                            if (isBeta)
                              Container(
                                decoration: BoxDecoration(
                                  color: const Color(0xFFFF96A3).withValues(alpha: .2),
                                  borderRadius: BorderRadius.circular(4),
                                ),
                                padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
                                margin: const EdgeInsets.only(left: 6),
                                child: Text(
                                  l10n.beta,
                                  style: const TextStyle(
                                    fontSize: 12,
                                    color: Color(0xFFFF4E64),
                                  ),
                                ),
                              ),
                          ],
                        ),
                        Padding(
                          padding: const EdgeInsets.only(top: 8.0),
                          child: Text(
                            subTitle,
                            textAlign: TextAlign.start,
                            style: TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.w400,
                              color: AppColors.appBlackColor.withValues(alpha: .56),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  if (isIconArrowRight)
                    Padding(
                      padding: const EdgeInsets.only(left: 4.0),
                      child: SvgPicture.asset(
                        'assets/svgs/arrow-right-menu.svg',
                        // width: 24,
                        // height: 24,
                        colorFilter: const ColorFilter.mode(
                          AppColors.darkPrimaryBackgroundColor,
                          BlendMode.srcIn,
                        ),
                      ),
                    ),
                ],
              ),
        ),
      ),
    );
  }

  AppBar _buildAppbar(AppLocalizations l10n) {
    return AppBar(
      titleSpacing: 0.0,
      backgroundColor: AppColors.lightPrimaryBackgroundColor,
      elevation: 0,
      leading: Padding(
        padding: const EdgeInsets.all(10.0),
        child: SizedBox(
          width: 40,
          child: MaterialButton(
            elevation: 0,
            highlightElevation: 0,
            hoverElevation: 0,
            hoverColor: AppColors.lightPrimaryBackgroundColor,
            onPressed: () => {
              Navigator.of(context).pop(),
            },
            color: AppColors.lightPrimaryBackgroundColor,
            padding: EdgeInsets.zero,
            shape: const CircleBorder(),
            child: SvgPicture.asset(
              'assets/svgs/arrow-back.svg',
            ),
          ),
        ),
      ),
      title: Text(
        l10n.callButtonOptions,
        style: const TextStyle(
          color: AppColors.darkPrimaryBackgroundColor,
          fontSize: 16,
          fontWeight: FontWeight.w600,
        ),
      ),
      centerTitle: true,
    );
  }
}
