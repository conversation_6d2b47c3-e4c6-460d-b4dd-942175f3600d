// ignore_for_file: use_setters_to_change_properties, empty_catches

import 'dart:io';
import 'dart:async';

import 'package:dio/dio.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:stickyqrbusiness/@common/common.dart';
import 'package:stickyqrbusiness/@const/config.dart';
import 'package:stickyqrbusiness/@core/core.dart';
import 'package:stickyqrbusiness/@core/store/notification-fcm/notification_cubit.dart';
import 'package:stickyqrbusiness/@share/apis.dart';
import 'package:stickyqrbusiness/@utils/utils.dart';

class AppFCM {
  static AppFCM? _instance;

  static final FirebaseMessaging _fcmInstance = FirebaseMessaging.instance;
  static FirebaseMessaging get fcmInstance => _fcmInstance;
  static late NotificationCubit _notificationCubit;

  static Timer? _throttleTimer;
  static const Duration _throttleDuration = Duration(seconds: 3);
  static bool _isThrottled = false;

  factory AppFCM() {
    return _instance ?? AppFCM._init();
  }

  AppFCM._init();

  static void fcmCubit(NotificationCubit notificationCubit) {
    _notificationCubit = notificationCubit;
  }

  static Future<AuthorizationStatus> requestPermission() async {
    final NotificationSettings rep = await _fcmInstance.requestPermission(
      alert: true,
      badge: true,
      provisional: false,
      sound: true,
    );
    AppLog.i('Permission Status ${rep.authorizationStatus}', classParent: 'AppFCM', logType: AppLoggerType.FUNCTION);
    return rep.authorizationStatus;
  }

  static Future<void> subscribeTopic(String topic1, String topic2) async {
    final prefs = await SharedPreferences.getInstance();

    AppLog.e(' Topic 1: $topic1');
    AppLog.e(' Topic 2: $topic2');
    await _fcmInstance.subscribeToTopic(topic1);
    await _fcmInstance.subscribeToTopic(topic2);
    await prefs.setString(AppConfig.localFCMTopicBID.key, topic1);
    await prefs.setString(AppConfig.localFCMTopicBIDUID.key, topic2);
    registerFCMTokenThrottled();
  }

  static Future<void> unSubscribeTopic() async {
    // Hủy timer throttle nếu có
    _throttleTimer?.cancel();

    final prefs = await SharedPreferences.getInstance();
    final localTopic1 = prefs.getString(AppConfig.localFCMTopicBID.key);
    final localTopic2 = prefs.getString(AppConfig.localFCMTopicBIDUID.key);
    try {
      if (localTopic1 != null) {
        await _fcmInstance.unsubscribeFromTopic(localTopic1);
      }
      if (localTopic2 != null) {
        await _fcmInstance.unsubscribeFromTopic(localTopic2);
      }
    } catch (e) {}
    await prefs.remove(AppConfig.localFCMTopicBID.key);
    await prefs.remove(AppConfig.localFCMTopicBIDUID.key);
    await _fcmInstance.deleteToken();
  }

  // Method để cleanup Timer khi cần
  static void dispose() {
    _throttleTimer?.cancel();
    _throttleTimer = null;
  }

  static void registerFCMTokenThrottled() {
    if (_isThrottled) {
      return;
    }
    _registerFCMToken();
    _isThrottled = true;
    _throttleTimer?.cancel();
    _throttleTimer = Timer(_throttleDuration, () {
      _isThrottled = false;
    });
  }

  static Future<void> _registerFCMToken() async {
    // Lấy FCM Token
    final token = await _fcmInstance.getToken();
    AppLog.e('FCM => Token: $token');
    if (token != null && token != '') {
      await uploadToken(token);
    }
    registerTokenRefresh();
  }

  static void registerTokenRefresh() {
    _fcmInstance.onTokenRefresh.listen((token) {
      AppLog.e('FCM => New  Token: $token');
      if (token != null && token != '') {
        uploadToken(token);
      }
    });
  }

  static void configure() {
    // Xử lý khi người dùng click vào thông báo
    FirebaseMessaging.onMessageOpenedApp.listen((RemoteMessage message) {
      AppLog.e(
        'onMessageOpenedApp',
        logType: AppLoggerType.FUNCTION,
        classParent: 'configure',
        obj: message,
      );

      message.data['message'] = message.notification?.body;

      onFCMOpenedTap(message.data);
    });

    /// on App open, in use //Lắng nghe tin nhắn khi app đang chạy
    FirebaseMessaging.onMessage.listen((message) {
      // if (message.notification != null) {
      //   print('Message also contained a notification: ${message.notification}');
      // }
      AppLog.e(
        'onMessage App Open: $message',
        logType: AppLoggerType.FUNCTION,
        classParent: 'configure',
        obj: message,
      );

      AppLog.e('onMessage: $message');
      AppLog.e('onMessage data: ${message.data}');
      // message.data['message'] = message.notification?.body;
      onFCMForeground(message.data);
    });

    // Xử lý tin nhắn khi app đang ở background
    FirebaseMessaging.onBackgroundMessage((RemoteMessage message) async {
      AppLog.e('onBackgroundMessage: $message');

      AppLog.e(
        'onBackgroundMessage: $message',
        logType: AppLoggerType.FUNCTION,
        classParent: 'configure',
        obj: message,
      );
      // message.data['message'] = message.notification?.body;
      onFCMForeground(message.data);
    });
  }

  /// on App open, in use
  static void onFCMForeground(Map<String, dynamic> jsonData) {
    AppLog.i('Message onFCMForeground: $jsonData', logType: AppLoggerType.FUNCTION, classParent: 'onFCMForeground');
    final fcmData = fcmJsonDecode(jsonData);
    // AppLog.e('data = ${jsonEncode(fcmData)}');
    AppLog.e('onFCMForeground: ${fcmData?.toJson()}');
    if (fcmData != null) {
      AppLog.i('TYPE ${fcmData.type}');
      _notificationCubit.onToastNotification(fcmData);
    }
  }

  /// on FCM Tap
  static void onFCMOpenedTap(Map<String, dynamic> jsonData) {
    // AppLog.e('onFCMOpenedTap: $jsonData', logType: AppLoggerType.FUNCTION, classParent: 'onFCMOpenedTap');
    final FcmData? fcmData = fcmJsonDecode(jsonData);
    AppLog.e('onFCMOpenedTap $fcmData');
    if (fcmData != null) {
      _notificationCubit.onTapNotification(fcmData);
      switch (fcmData.type) {
        case TypeNotif.businessClaimPointSendNotification:
          break;
        case TypeNotif.businessOrdersConfirmed:
          break;
        default:
      }
    }
  }

  static void onTerminatedAppTap() {
    FirebaseMessaging.instance.getInitialMessage().then((RemoteMessage? message) {
      AppLog.i(
        'FirebaseMessaging getInitialMessage ',
        logType: AppLoggerType.FUNCTION,
        classParent: 'configure',
        obj: message,
      );
      if (message != null) {
        final fcmData = fcmJsonDecode(message.data) ?? FcmData();
        _notificationCubit.onTapNotification(fcmData);
      }
    });
  }

  static FcmData? fcmJsonDecode(Map<String, dynamic> jsonData) {
    try {
      // final message = jsonData['message'];
      // AppLog.e('FCM DATA: $jsonData $message');
      // final String json = jsonData['body'] ?? jsonData['data'] ?? jsonData;
      final FcmData obj = FcmData.fromRawJson(jsonData);
      // if (message != null) {
      //   obj.message = message;
      // }
      return obj;
    } catch (e) {
      return null;
    }
  }

  static Future<dynamic> uploadToken(String? token) async {
    try {
      if (token == null || token.isEmpty) return null;

      final response = await AppHttp.request(AppAPI.fcmToken, method: APIRequestMethod.POST, body: {
        'token': token, // UNIQUE TOKEN
        'platform': Platform.isAndroid
            ? 'ANDROID'
            : Platform.isIOS
                ? 'IOS'
                : 'WEB', // WEB ANDROID IOS
        'isActive': true,
      });
      AppBased.doAuditLogging(
        pageName: 'FCM uploadToken',
        pageUrl: 'upload-token',
        pageAction: 'uploadToken',
        metadata: {},
      );
      AppLog.e('response uploadToken: $response');
      return response.data;
      // return Announcement.fromJson(response.data as Map<String, dynamic>);
    } on DioException catch (e) {
      AppLog.e('DioException uploadToken : $e');
      // AppError.dioResponseError(e);
    }
    return null;
  }
}
