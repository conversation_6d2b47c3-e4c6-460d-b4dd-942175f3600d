{"@@locale": "vi", "appName": "StickyQR", "homePageTitle": "Trang chủ", "loginSignIn": "<PERSON><PERSON><PERSON>", "loginEmail": "Email", "loginEmailAddress": "Đ<PERSON>a chỉ email", "loginPassword": "<PERSON><PERSON><PERSON>", "loginForgotPassword": "<PERSON>uên mật khẩu?", "loginSignUp": "<PERSON><PERSON><PERSON> ký", "loginHaveAccount": "Bạn chưa có tài k<PERSON>n?", "emailInvalid": "<PERSON><PERSON> h<PERSON> l<PERSON>", "emailRequired": "<PERSON><PERSON> l<PERSON> b<PERSON> bu<PERSON>c", "phoneInvalid": "<PERSON><PERSON> điện tho<PERSON><PERSON> không hợp lệ", "phoneRequired": "<PERSON><PERSON> điện thoại là bắt buộc", "phoneIncorrect": "<PERSON><PERSON> điện tho<PERSON>i không ch<PERSON>h xác", "passwordRequired": "<PERSON><PERSON><PERSON> kh<PERSON>u là bắt buộc", "registerSignUp": "<PERSON><PERSON><PERSON> ký", "registerSignIn": "<PERSON><PERSON><PERSON>", "registerEmailAddress": "Đ<PERSON>a chỉ email", "registerPassword": "<PERSON><PERSON><PERSON>", "registerHaveAccount": "Đã có tài k<PERSON>n?", "forgotPassword": "<PERSON><PERSON><PERSON><PERSON> mật k<PERSON>u", "forgotPasswordDescription": "<PERSON><PERSON> lòng nhập địa chỉ email liên kết với tài khoản của bạn", "send": "<PERSON><PERSON><PERSON>", "resendCode": "<PERSON><PERSON><PERSON> l<PERSON>i", "verify": "<PERSON><PERSON><PERSON>", "tryAgain": "<PERSON><PERSON><PERSON> lại", "codeRequired": "<PERSON><PERSON> x<PERSON>c nhận là bắt buộc", "confirm": "<PERSON><PERSON><PERSON>", "resetPassword": "Đặt lại mật khẩu", "backToSignIn": "Quay lại đăng nh<PERSON>p", "passwordChanged": "<PERSON><PERSON><PERSON> khẩu đã đư<PERSON>c thay đổi", "passwordChangedDescription": "<PERSON><PERSON><PERSON> kh<PERSON>u của bạn đã đư<PERSON><PERSON> thay đổi thành công", "enterCode": "<PERSON>h<PERSON><PERSON> mã", "enterCodeDescription": "<PERSON><PERSON><PERSON><PERSON> mã xác nhận để đặt lại mật khẩu của bạn", "incorrectVerificationCode": "<PERSON>ã xác nhận không ch<PERSON>h xác", "enterPasswordDescription": "Tạo một mật khẩu có ít nhất 8 ký tự", "payAtFrontDesk": "<PERSON><PERSON> to<PERSON> tại qu<PERSON>y", "payOnline": "<PERSON><PERSON> to<PERSON> trực tuyến", "cancelBooking": "Hủy đặt phòng", "apply": "<PERSON><PERSON>", "enterYourCode": "<PERSON><PERSON><PERSON><PERSON> mã của bạn", "thanksYou": "<PERSON>n cảm ơn!", "later": "Sau", "notFound": "<PERSON><PERSON><PERSON><PERSON> tìm thấy", "search": "<PERSON><PERSON><PERSON>", "select": "<PERSON><PERSON><PERSON>", "selectCountry": "<PERSON><PERSON><PERSON>ia", "firstName": "Họ", "lastName": "<PERSON><PERSON><PERSON>", "gender": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>h", "dateOfBirth": "<PERSON><PERSON><PERSON>", "email": "Email", "phone": "<PERSON><PERSON> đi<PERSON>n tho<PERSON>i", "password": "<PERSON><PERSON><PERSON>", "male": "Nam", "female": "<PERSON><PERSON>", "others": "K<PERSON><PERSON><PERSON>", "add": "<PERSON><PERSON><PERSON><PERSON>", "edit": "Chỉnh sửa", "profile": "<PERSON><PERSON> sơ", "editProfile": "Chỉnh s<PERSON>a <PERSON> sơ", "emailAddress": "Đ<PERSON>a chỉ email", "updateEmail": "<PERSON><PERSON><PERSON> nh<PERSON> email", "updateEmailMessage": "<PERSON><PERSON><PERSON> tôi sẽ gửi cho bạn một email để xác nhận địa chỉ email của bạn", "addAMobileNumber": "<PERSON>hê<PERSON> số điện thoại di động", "addAMobileNumberMessage": "<PERSON><PERSON><PERSON> tôi sẽ gửi cho bạn một mã xác nhận qua tin nhắn để xác minh số điện thoại của bạn", "country": "Quốc gia", "mobileNumber": "<PERSON><PERSON> điện thoại di động", "imageFromGalleryTitle": "Ảnh từ thư viện", "takeAPhotoTitle": "<PERSON><PERSON><PERSON>", "firstNameRequired": "<PERSON><PERSON> là bắt buộc", "lastNameRequired": "<PERSON><PERSON><PERSON> là b<PERSON><PERSON> bu<PERSON>c", "notUpdatedYet": "<PERSON><PERSON><PERSON> c<PERSON> nh<PERSON>t", "yearOld": "<PERSON><PERSON><PERSON>", "cancel": "<PERSON><PERSON><PERSON>", "save": "<PERSON><PERSON><PERSON>", "ok": "Đồng ý", "updatePassword": "<PERSON><PERSON><PERSON> nh<PERSON>t mật kh<PERSON>u", "currentPassword": "<PERSON><PERSON><PERSON><PERSON> hiện tại", "newPassword": "<PERSON><PERSON><PERSON> mới", "confirmPassword": "<PERSON><PERSON><PERSON>n mật kh<PERSON>u", "confirmPasswordRequired": "<PERSON><PERSON><PERSON> nhận mật khẩu là bắt buộc", "confirmPasswordInvalid": "<PERSON><PERSON><PERSON> nh<PERSON>n mật khẩu không khớp", "newPasswordInvalid": "<PERSON><PERSON>t khẩu mới không thể trùng với mật khẩu hiện tại", "currentPasswordRequired": "<PERSON><PERSON><PERSON> kh<PERSON>u hiện tại là bắt buộc", "newPasswordRequired": "<PERSON><PERSON><PERSON> kh<PERSON>u mới là bắt buộc", "register": "<PERSON><PERSON><PERSON> ký", "notifications": "<PERSON><PERSON><PERSON><PERSON> báo", "youHaveNoNotification": "Bạn không có thông báo", "notifiEmptyMessage": "Bạn không có thông báo trong lúc này.", "markAsRead": "<PERSON><PERSON><PERSON> dấu là đã đọc", "yes": "<PERSON><PERSON>", "no": "K<PERSON>ô<PERSON>", "welcome": "<PERSON><PERSON><PERSON> m<PERSON>ng", "newStatus": "<PERSON><PERSON><PERSON>", "confirmedStatus": "<PERSON><PERSON><PERSON>", "checkedInStatus": "Đã check-in", "checkedOutStatus": "Đã check-out", "cancelledStatus": "<PERSON><PERSON> hủy", "noShowStatus": "<PERSON><PERSON><PERSON><PERSON> xu<PERSON> hi<PERSON>", "markAsUnread": "<PERSON><PERSON><PERSON> dấu là chưa đ<PERSON>c", "confirmLogoutMsg": "Bạn có chắc chắn muốn đăng xuất?", "confirmDeleteMsg": "Bạn có chắc chắn muốn xóa?", "delete": "Xóa", "signInOPT": "Đ<PERSON>ng nhập OTP", "signInPhoneOrUserName": "<PERSON><PERSON> điện thoại hoặc tên đăng nhập", "otp": "OTP", "forgotPhoneOrEmail": "<PERSON><PERSON> điện thoại hoặc email", "back": "Quay lại", "next": "<PERSON><PERSON><PERSON><PERSON> theo", "emailPhoneRequired": "Email hoặc số điện thoại là bắt buộc", "emailPhoneInvalid": "Email hoặc số điện tho<PERSON>i không hợp lệ", "phoneNumber": "<PERSON><PERSON> đi<PERSON>n tho<PERSON>i", "verificationCode": "<PERSON><PERSON> x<PERSON>n", "signInSignUp": "<PERSON><PERSON>ng nhập hoặc đăng ký vào StickyQR", "youHave": "<PERSON><PERSON><PERSON> ch<PERSON><PERSON> thi<PERSON>t lập mật kh<PERSON>u", "passwordIsUsed": "<PERSON><PERSON><PERSON> kh<PERSON><PERSON> đư<PERSON><PERSON> sử dụng để bảo vệ và xác minh tài khoản của bạn.", "setUpNow": "<PERSON><PERSON><PERSON><PERSON> l<PERSON>p ngay", "switchAccount": "Chuyển đổi tài k<PERSON>n", "businessName": "<PERSON><PERSON><PERSON> c<PERSON><PERSON> hàng", "businessNameRequired": "<PERSON><PERSON><PERSON> c<PERSON><PERSON> hàng là bắt buộc", "codeIsIncorrect": "Mã không ch<PERSON>h xác", "logOut": "<PERSON><PERSON><PERSON> xu<PERSON>", "lockScreen": "<PERSON><PERSON><PERSON><PERSON> màn hình", "codesScanned": "# Số mã đã quét", "today": "<PERSON><PERSON><PERSON> nay", "thisMonth": "<PERSON><PERSON><PERSON><PERSON>", "sticky": "<PERSON>y", "enterPoints": "<PERSON><PERSON><PERSON><PERSON> đi<PERSON>", "enterPointsRequired": "<PERSON><PERSON><PERSON><PERSON> là b<PERSON> buộc", "points": "<PERSON><PERSON><PERSON><PERSON>", "qty": "Số lượng", "show": "<PERSON><PERSON><PERSON> thị QR", "createAndShow": "Tạo và hiển thị mã QR.", "print": "In", "createAndPrint": "Tạo và in mã QR.", "printerSetting": "Cài đặt máy in", "customer": "<PERSON><PERSON><PERSON><PERSON>", "go": "<PERSON><PERSON>", "customers": "<PERSON><PERSON><PERSON><PERSON>", "settings": "Cài đặt", "enterPhone": "<PERSON><PERSON><PERSON><PERSON> số điện thoại", "qrCodeManagement": "<PERSON><PERSON><PERSON><PERSON> lý mã QR", "stickyNomal": "StickyQR", "scanQR": "<PERSON><PERSON><PERSON> mã QR để nhận điểm.", "notQRCode": "Mã QR không hợp lệ!", "pleaseEnterPasscode": "<PERSON><PERSON> lòng nhập mã", "successfully": "<PERSON><PERSON><PERSON><PERSON> công", "enterPhoneNumber": "<PERSON><PERSON><PERSON><PERSON> số điện thoại của bạn", "invalidPhoneOrPass": "<PERSON><PERSON> điện thoại hoặc mật khẩu không hợp lệ!", "empty": "<PERSON><PERSON><PERSON><PERSON>", "printEmty": "<PERSON><PERSON><PERSON><PERSON> tìm thấy máy in", "printerName": "<PERSON><PERSON><PERSON> in", "printerNameRequired": "<PERSON><PERSON><PERSON> in là bắt buộc", "scan": "<PERSON><PERSON><PERSON>", "ipSetting": "Cài đặt IP", "noPrinter": "<PERSON><PERSON><PERSON><PERSON> tìm thấy máy in - Thử lại hoặc cài đặt IP", "ipAddress": "Địa chỉ IP", "port": "Cổng", "addPrinter": "<PERSON><PERSON><PERSON><PERSON> in", "editPrinter": "<PERSON><PERSON><PERSON> in", "printTest": "In thử", "unableToConnect": "<PERSON><PERSON><PERSON><PERSON> thể kết nối", "ipAddressRequired": "Địa chỉ IP là bắt buộc", "wifiEthernetPrinters": "Máy in Wifi/ Ethernet", "labelsTemplate": "<PERSON><PERSON>", "defaultText": "Mặc định", "instanceStickyQR": "StickyQR", "customTemplate": "<PERSON><PERSON>", "printers": "<PERSON><PERSON><PERSON> in", "printer": "<PERSON><PERSON><PERSON> in", "size": "<PERSON><PERSON><PERSON>", "qr": "QR", "align": "<PERSON><PERSON>n chỉnh", "left": " Trái ", "right": "<PERSON><PERSON><PERSON>", "width": "Rộng", "content": "<PERSON><PERSON>i dung", "line": "Dòng", "printerIsRequired": "M<PERSON><PERSON> in là bắt buộc", "claimed": "<PERSON>ã này chỉ có thể được đổi một lần", "printersSettings": "Cài đặt máy in", "noPrintersAreAvailable": "<PERSON><PERSON><PERSON><PERSON> có má<PERSON> in nào khả dụng", "pressToSetUpAPrinter": "Nhấn (+) để cài đặt máy in", "enterCharacters": "<PERSON><PERSON><PERSON><PERSON> v<PERSON><PERSON> bản", "ipAddressIncorrect": "Địa chỉ IP không ch<PERSON>h xác", "connectToPrinter": "<PERSON><PERSON><PERSON><PERSON> thể in. <PERSON><PERSON> lòng kiểm tra cài đặt hoặc máy in.", "scanning": "<PERSON><PERSON> quét...", "setUp": "Cài đặt ngay", "msgSnackBar": "<PERSON><PERSON><PERSON> thi<PERSON>t lập m<PERSON> in hoặc mẫu in", "bottomContent": "<PERSON><PERSON><PERSON> dung dưới cùng", "point": "<PERSON><PERSON><PERSON><PERSON>", "redeem": "Đ<PERSON>i", "total": "<PERSON><PERSON><PERSON> cộng", "error": "Lỗi!", "close": "Đ<PERSON><PERSON>", "guest": "<PERSON><PERSON><PERSON><PERSON>", "photo": "Ảnh", "nameReward": "<PERSON><PERSON><PERSON> đãi", "nameRewardRequired": "<PERSON><PERSON><PERSON> là b<PERSON><PERSON> bu<PERSON>c", "editReward": "Chỉnh sửa ưu đãi", "deleteReward": "<PERSON><PERSON><PERSON>u đãi", "createReward": "<PERSON><PERSON><PERSON> đãi", "rewards": "Ưu đãi", "redeemRewards": "<PERSON><PERSON><PERSON> ưu đãi", "redemptionComplete": "<PERSON><PERSON><PERSON> thành đổi ưu đãi", "redemptionSuccessful": "Đổi ưu đãi thành công!", "followingRewards": "cho các ưu đãi sau?", "rewardsToRedeem": "<PERSON><PERSON><PERSON> không có ưu đãi để đổi", "currentlyNoRewards": "<PERSON><PERSON><PERSON> không c<PERSON> <PERSON>u đãi", "opps": "Oops!", "upload": "<PERSON><PERSON><PERSON>", "addReward": "<PERSON><PERSON><PERSON> đãi", "warning": "<PERSON><PERSON><PERSON> b<PERSON>o", "fileIsLarge": "<PERSON><PERSON><PERSON> quá lớn", "labelTemplate": "<PERSON><PERSON>", "arrangeRewards": "<PERSON><PERSON><PERSON> x<PERSON>p <PERSON> đãi", "printersAvailable": "<PERSON><PERSON><PERSON><PERSON> có m<PERSON> in khả dụng", "selectPrinter": "<PERSON><PERSON><PERSON> in", "dpi": "Chọn DPI máy in", "dpiRequired": "DPI máy in là bắt buộc", "sendCode": "<PERSON><PERSON><PERSON> mã", "textMessage": "<PERSON> v<PERSON>n bản", "voiceCall": "<PERSON><PERSON><PERSON><PERSON><PERSON> tho<PERSON>i", "receiveCodeTitle": "<PERSON><PERSON><PERSON>ng nhận đư<PERSON>c mã?", "verificationCodeVia": "Nhận mã xác minh qua", "enterSendCodeTitle": "<PERSON><PERSON><PERSON><PERSON> mã đã gửi đến thiết bị của bạn", "resendTextTitle": "<PERSON><PERSON> lòng nhập mã đã gửi qua tin nhắn tới điện thoại{andemail}. <PERSON><PERSON> nhận cuộc gọi điện thoại thay thế,", "@resendTextTitle": {"description": "và email", "placeholders": {"andemail": {"type": "String"}}}, "andEmail": " và email", "resendCallTitle": "<PERSON><PERSON> lòng nhập mã bạn nghe được trong cuộc gọi. <PERSON><PERSON> nhận mã qua tin nhắn thay thế,", "resendTextCode": "<PERSON><PERSON><PERSON> lại tin nh<PERSON>n", "resendCallCode": "<PERSON><PERSON><PERSON> l<PERSON>i", "tapHere": "nhấn vào đây.", "successForgor": "<PERSON><PERSON><PERSON> khẩu đã đư<PERSON><PERSON> cập nhật", "codeScanEmptyTitle": "<PERSON><PERSON><PERSON><PERSON> có mã nào đ<PERSON><PERSON><PERSON> quét", "passWordRequiedCharacter": "<PERSON><PERSON>t khẩu ph<PERSON>i có ít nhất 6 ký tự.", "passWordNewRequiedCharacter": "<PERSON><PERSON>t khẩu mới phải có ít nhất 6 ký tự.", "staffEdit": "<PERSON><PERSON><PERSON> thay đổi của bạn đã đ<PERSON><PERSON><PERSON> lưu", "staffCreate": "<PERSON><PERSON>ân viên đã đư<PERSON><PERSON> tạo", "staffEditTitle": "Chỉnh sửa nhân viên", "staffCreateTitle": "Tạo nhân viên", "staffs": "Nhân viên", "name": "<PERSON><PERSON><PERSON>", "nameRequired": "<PERSON><PERSON><PERSON> là b<PERSON><PERSON> bu<PERSON>c", "staffsEmptyTitle": "<PERSON><PERSON><PERSON><PERSON> có nhân viên", "businessProfile": "<PERSON><PERSON><PERSON>ng tin cửa hàng", "timezone": "<PERSON><PERSON><PERSON> giờ", "currency": "Đơn vị tiền tệ", "countryCode": "Mã quốc gia", "street": "<PERSON><PERSON><PERSON>", "city": "Tỉnh/Thành phố", "state": "Mã tỉnh", "optional": "t<PERSON><PERSON>n", "zipcode": "<PERSON><PERSON> b<PERSON>u đi<PERSON>n", "saved": "<PERSON><PERSON> l<PERSON>", "rewardHasBeenCreated": "Ưu đãi đã đư<PERSON><PERSON> tạo", "rewardHasBeenUpdated": "Ưu đãi đã đư<PERSON><PERSON> cập nhật", "rewardHasBeenDeleted": "Ưu đãi đã đư<PERSON><PERSON> xóa", "thePointsHasBeenAdded": "Đã tặng {points}", "@thePointsHasBeenAdded": {"description": "points", "placeholders": {"points": {"type": "String"}}}, "preview": "<PERSON><PERSON>", "setPassword": "Đặt mật khẩu người dùng! (t<PERSON><PERSON> ch<PERSON><PERSON>)", "activities": "<PERSON><PERSON><PERSON> s<PERSON>", "done": "<PERSON><PERSON><PERSON> th<PERSON>", "toDay": "<PERSON><PERSON><PERSON> nay", "thisWeek": "<PERSON><PERSON><PERSON>", "thisMonthActivities": "<PERSON><PERSON><PERSON><PERSON>", "custom": "Tuỳ chỉnh", "all": "<PERSON><PERSON><PERSON> c<PERSON>", "scanned": "<PERSON><PERSON>t mã", "rewardsRedeemed": "<PERSON><PERSON><PERSON> điểm", "pointsEarned": "<PERSON><PERSON><PERSON><PERSON> thưởng", "codesScannedTitle": "<PERSON><PERSON> quét", "active": "<PERSON><PERSON><PERSON> đ<PERSON>", "inactive": "<PERSON><PERSON><PERSON><PERSON> hoạt động", "announcements": "<PERSON><PERSON><PERSON><PERSON> báo", "announcementEmptyTitle": "<PERSON><PERSON><PERSON>ng có thông báo", "announcementEdit": "<PERSON>hay đổi của bạn đã đ<PERSON><PERSON><PERSON> lưu", "announcementCreate": "<PERSON><PERSON><PERSON><PERSON> báo đã đ<PERSON><PERSON><PERSON> tạo", "editAnnouncement": "Chỉnh sửa thông báo", "deleteAnnouncement": "Thông báo đã xoá thành công", "createAnnouncement": "<PERSON><PERSON><PERSON> thông báo", "description": "<PERSON><PERSON>", "descriptionIsRequired": "<PERSON><PERSON> tả là b<PERSON><PERSON> buộc", "images": "<PERSON><PERSON><PERSON> (t<PERSON><PERSON>n)", "announcement": "<PERSON><PERSON><PERSON><PERSON> báo", "selectIcons": "<PERSON><PERSON><PERSON> bi<PERSON> t<PERSON>", "limitImages": "<PERSON><PERSON> lượng hình <PERSON>nh không đ<PERSON><PERSON><PERSON> n<PERSON> hơn {limit}", "@limitImages": {"description": "limit images", "placeholders": {"limit": {"type": "String"}}}, "activitiesEmpty": "<PERSON><PERSON><PERSON><PERSON> có dữ liệu để hiển thị ngay bây giờ!", "voidType": "<PERSON><PERSON> thu hồi", "voidTypeTitle": "<PERSON><PERSON> h<PERSON>", "addCustomer": "Bạn có muốn thêm khách hàng này vào hệ thống?", "customerName": "<PERSON><PERSON><PERSON> (<PERSON><PERSON><PERSON>)", "lessThanOne": "<PERSON><PERSON><PERSON><PERSON> không đượ<PERSON> nhỏ hơn 1", "confirmVoidMsg": "Bạn có muốn thu hồi điểm giao dịch này không?", "createdBy": "<PERSON><PERSON><PERSON><PERSON> tạo bởi", "voidedBy": "<PERSON><PERSON> hồi bởi", "date": "<PERSON><PERSON><PERSON>", "by": "bởi", "rewardsTitle": "Ưu đãi đã đổi", "copied": "Đã sao chép.", "enterPointsLabel": "[Points]", "arrangeAnnouncemnts": "<PERSON><PERSON><PERSON> xếp thông báo", "language": "<PERSON><PERSON><PERSON>", "account": "<PERSON><PERSON><PERSON>", "signOut": "<PERSON><PERSON><PERSON> xu<PERSON>", "business": "<PERSON><PERSON><PERSON> h<PERSON>", "english": "<PERSON><PERSON><PERSON><PERSON>", "vietnamese": "Tiếng <PERSON>", "yourName": "Họ và tên", "yourNameRequired": "Họ và tên là bắt buộc", "changePassword": "<PERSON>hay đ<PERSON>i mật kh<PERSON>u", "changePasswordALike": "<PERSON><PERSON>t khẩu mới không thể trùng với mật khẩu hiện tại của bạn", "continueTitle": "<PERSON><PERSON><PERSON><PERSON>", "getCodeBy": "Nhận mã bằng cách", "phoneCall": "<PERSON><PERSON><PERSON> đ<PERSON>", "useAnotherMethod": "Phương ph<PERSON>p kh<PERSON>c", "receiveCode": "<PERSON><PERSON><PERSON>ng nhận đư<PERSON>c mã?", "enterEmail": "<PERSON><PERSON><PERSON><PERSON> địa chỉ email", "codeSendEmailTitle": "Nhập mã chúng tôi đã gửi qua email tới", "codeCallPhoneTitle": "<PERSON><PERSON><PERSON><PERSON> mã đư<PERSON><PERSON> g<PERSON> đến", "codeSMSPhoneTitle": "Nhập mã chúng tôi đã gửi qua SMS tới", "checkNetwork": "<PERSON><PERSON><PERSON> tra kết nối mạng của bạn và thử lại", "choose": "<PERSON><PERSON><PERSON>", "locationAccess": "<PERSON><PERSON> lòng cho phép truy cập vị trí hoặc bật vị trí thiết bị của bạn", "map": "<PERSON><PERSON><PERSON>", "change": "<PERSON><PERSON> đ<PERSON>i", "enterPassword": "<PERSON><PERSON><PERSON><PERSON> mật kh<PERSON>u", "checkConfirmPasswod": "<PERSON><PERSON><PERSON> kh<PERSON>u không khớp", "confirmNewPassword": "<PERSON><PERSON><PERSON> nh<PERSON>n mật khẩu mới", "confirmNewPasswordRequired": "<PERSON><PERSON><PERSON> nhận mật khẩu mới là bắt buộc", "confirmPassWordNewRequiedCharacter": "<PERSON><PERSON><PERSON> nhận mật khẩu mới phải có ít nhất 6 ký tự", "enterStreet": "<PERSON><PERSON><PERSON><PERSON> tên đ<PERSON>", "referralProgram": "<PERSON><PERSON><PERSON><PERSON> trình giới thiệu", "pointsForReferrerUser": "<PERSON><PERSON><PERSON><PERSON> cho người giới thiệu", "pointsForReferrerUserRequired": "<PERSON><PERSON><PERSON><PERSON> cho người giới thiệu là bắt buộc", "pointsForReferrerUserValid": "<PERSON><PERSON><PERSON><PERSON> không đ<PERSON> nhỏ hơn 0", "pointsForReferrerUserSubtitle": "<PERSON><PERSON> bạn giới thiệu cho một ai đó và họ hoàn thành một nhiệm vụ cụ thể, bạn sẽ nhận được những điểm thưởng.", "pointsForReferredUser": "<PERSON><PERSON><PERSON><PERSON> cho người đư<PERSON><PERSON> giới thiệu", "pointsForReferredUserRequired": "<PERSON><PERSON><PERSON><PERSON> cho ngườ<PERSON> đư<PERSON><PERSON> giới thiệu là bắt buộc", "pointsForReferredUserSubtitle": "<PERSON>hi bạn được giới thiệu bởi một ai đó và hoàn thành một nhiệm vụ cụ thể, bạn sẽ nhận được những điểm thưởng.", "requiredPointsForCompletion": "<PERSON><PERSON><PERSON><PERSON> cần đạt để hoàn thành", "requiredPointsForCompletionRequired": "<PERSON><PERSON><PERSON><PERSON> cần đạt để hoàn thành là bắt buộc", "requiredPointsForCompletionSubtitle": "<PERSON><PERSON> hoàn thành quá trình giới thiệu, bạn cần tích luỹ số điểm này. Tiếp tục kiếm điểm bằng cách tham gia các hoạt động của cửa hàng và đạt được những mục tiêu đề ra.", "referralProgramTitle": "<PERSON><PERSON><PERSON>ng trình tích luỹ điểm dành cho các thành viên khi mời bạn bè của họ tích điểm tại cửa hàng", "referralProgramEnabled": "<PERSON><PERSON><PERSON> t<PERSON><PERSON> năng \"Ch<PERSON>ơng trình giới thiệu\" để quảng cáo nhằm thu hút nhiều thành viên, tích luỹ điểm và đổi phần thưởng tại cửa hàng.", "remove": "<PERSON><PERSON><PERSON> h<PERSON>nh", "noteRequiredPoint": "Lưu ý: <PERSON><PERSON><PERSON> bạn thiết lập \"Đ<PERSON>ểm cần đạt = 0\" thì người giới thiệu và được giới thiệu sẽ nhận thưởng ngay lập tức.", "referrerType": "<PERSON><PERSON><PERSON><PERSON> thi<PERSON>u", "addIcon": "<PERSON><PERSON><PERSON><PERSON> bi<PERSON> t<PERSON>", "thumbnail": "<PERSON><PERSON><PERSON> n<PERSON> bật", "addPreviewAnnouncementIcon": "<PERSON><PERSON> tr<PERSON><PERSON><PERSON> thông báo", "image": "<PERSON><PERSON><PERSON>", "imageIsRequired": "<PERSON><PERSON><PERSON> là b<PERSON> buộc", "useThumbnail": "Sử dụng một hình ảnh làm hình thu nhỏ.", "defaultDescription": "<PERSON><PERSON><PERSON> c<PERSON>a hàng với một mô tả ngắn.", "uploadImage": "<PERSON><PERSON><PERSON> lên h<PERSON>", "editImage": "Chỉnh sửa hình <PERSON>nh", "contentWillDisplayedUsers": "<PERSON><PERSON><PERSON> dung sẽ được hiển thị cho khách hàng.", "indicateOrHideAnnouncement": "Hiển thị hoặc ẩn thông báo cho khách hàng.", "donForgetToTap": "<PERSON><PERSON><PERSON> quên bấm vào nút", "buttonSeeHow": "để thấy nó đư<PERSON><PERSON> hiển thị thế nào với kh<PERSON>ch hàng.", "signageEditItemsMessages": "<PERSON><PERSON><PERSON> và sắp xếp phần thưởng bạn muốn hiển thị trên mẫu", "template": "Mẫu", "selectAll": "<PERSON><PERSON><PERSON> tất cả", "printPreview": "Xem trước & In", "dontForgetTapThe": "<PERSON><PERSON><PERSON> quên nhấn vào nút", "buttonToSeeHowTheTemp": "để xem bản mẫu sẽ hiển thị như thế nào", "signage": "Mẫu in ấn", "couterCards": "Thẻ để bàn", "poweredBy": "Powered by", "share": "<PERSON><PERSON> sẻ", "getStartedCounterCard": "Counter card", "counterCardTitleContent": "Thẻ để bàn {number}", "@counterCardTitleContent": {"description": "", "placeholders": {"number": {"type": "String"}}}, "contentTooltip": "<PERSON><PERSON>i dung sẽ được hiển thị cho người dùng.", "title": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON>", "titleRequired": "Ti<PERSON><PERSON> đề là b<PERSON><PERSON> buộc", "subtitleRequired": "<PERSON><PERSON> tả là b<PERSON><PERSON> buộc", "qrCodeContent": "Nội dung mã QR", "qrCodeContentTooltip": "<PERSON><PERSON><PERSON><PERSON> liên kết để thay đổi mã QR cho phù hợp với nội dung quảng cáo của bạn.", "url": "URL", "urlIsRequired": "URL bà lắt buộc", "footer": "<PERSON><PERSON><PERSON><PERSON> trang", "footerTooltip": "<PERSON><PERSON>n thị hoặc ẩn cuối trang.", "forgetTo": "<PERSON><PERSON><PERSON> quên nhấn vào", "previewAndPrint": "\"Xem trước & in\"", "templateWillDisplay": "để xem mẫu sẽ hiển thị như thế nào", "previewPrint": "Xem trước & In", "previewCounterCard1": "<PERSON>em trước thẻ để bàn #1", "units": "Đơn vị", "height": "cao", "urlIsIncorrect": "URL không chính xác", "getStarted": "<PERSON><PERSON><PERSON> đ<PERSON>u", "downloadThe": "<PERSON><PERSON><PERSON> về <PERSON>ng dụng", "app": "<PERSON>ng dụng", "givenPoints": "Tặng điểm", "scanToJoinOur": "<PERSON><PERSON>t mã để tham gia chương trình ưu đãi", "closeBusiness": "<PERSON><PERSON><PERSON> t<PERSON>", "closeBusinessMsg1": "<PERSON><PERSON><PERSON> bạn xoá tài khoản doanh nghiệp của mình, tất cả thông tin của khách hàng sẽ bị xóa vĩnh viễn.", "closeBusinessMsg2": "Bạn vẫn có thể mở một tài khoản doanh nghiệp mới với cùng một số điện thoại.", "definitelyCloseMyBusiness": "<PERSON><PERSON><PERSON> chắn đóng tài <PERSON>n", "pleaseEnterTheCode": "<PERSON><PERSON> lòng nhập mã được nhắn tin vào điện thoại của bạn", "toReceiveAPhoneCallInstead": "<PERSON><PERSON> nhận một cuộc gọi điện thoại thay thế,", "msgAlertCall": "<PERSON><PERSON> lòng nhập mã bạn nghe được trong cuộc gọi. Thay vào đó, để nhận nó bằng văn bản,", "textAgain": "<PERSON><PERSON><PERSON><PERSON> lại", "callAgain": "<PERSON><PERSON><PERSON> l<PERSON>i", "staffDeleted": "<PERSON><PERSON>ân viên đã bị xóa", "deleteStaff": "Xoá nhân viên", "rewardsRedeemedTemp2": "Ưu đãi", "cutLabel": "<PERSON><PERSON><PERSON>", "customize": "TUỲ CHỈNH", "update": "<PERSON><PERSON><PERSON>", "updateProfileTitle": "<PERSON><PERSON><PERSON> cập nhật thông tin cửa hàng. <PERSON><PERSON><PERSON><PERSON> này sẽ giúp khách hàng dễ dàng tiếp cận hơn.", "printTestNote": "Điều chỉnh máy in khi kiểm tra in thử không chính xác.", "adjust": "<PERSON><PERSON><PERSON><PERSON> chỉnh", "printTestAllCase": "Print test all case", "notePrinterChangeSizeText1": "Lưu ý: <PERSON><PERSON> bạn thay đổi ", "labelSize": "<PERSON><PERSON><PERSON><PERSON>", "notePrinterChangeSizeText2": ", bản in ra có thể không chính xác. Bạn cần điều chỉnh kích thước in sao cho phù hợp với kích thước nhãn (Nhấn vào nút \"Điều chỉnh\" bên dưới)", "againExitApp": "<PERSON><PERSON><PERSON> lần n<PERSON><PERSON> để tho<PERSON>t", "unsavedChanges": "<PERSON><PERSON> đ<PERSON>i chưa đ<PERSON><PERSON><PERSON> l<PERSON>u", "unsavedChangesSubtitle": "<PERSON><PERSON><PERSON><PERSON> thay đổi bạn đã thực hiện có thể không đư<PERSON><PERSON> lư<PERSON>.", "saveChanges": "<PERSON><PERSON><PERSON> thay đổi", "leaveWithoutSaving": "Rời khỏi mà không lưu", "management": "<PERSON><PERSON><PERSON><PERSON> lý", "detail": "<PERSON> ti<PERSON>", "newCustomer": "<PERSON><PERSON><PERSON><PERSON> mới", "returning": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>", "pending": "<PERSON>ờ <PERSON>", "planExceeded": "<PERSON>ần hành động!", "notiUpgradeMsgScanQR": "<PERSON><PERSON> một Khách hàng vừa mới quét QR Code. <PERSON><PERSON> nhiên gói của bạn đã vượt hạn mức cho phép.", "notiUpgradeMsgRefferal": "<PERSON><PERSON> một Khách hàng vừa tham gia Chương trình giới thiệu kiếm điểm. <PERSON><PERSON> nhiên gói của bạn đã vượt hạn mức cho phép.", "pendingNote": "<PERSON><PERSON> hoạch kinh doanh v<PERSON><PERSON><PERSON> quá giới hạn cho phép.", "type": "<PERSON><PERSON><PERSON>", "sourceQRScanned": "Quét mã QR", "sourceReferral": "<PERSON><PERSON><PERSON><PERSON> thi<PERSON>u", "quantity": "Số lượng: ", "printerQueue": "<PERSON><PERSON><PERSON> đợi m<PERSON>y in", "printing": "Đang in", "queue": "Đ<PERSON><PERSON>", "basic": "<PERSON><PERSON> bản", "gold": "<PERSON><PERSON><PERSON>", "platinum": "<PERSON><PERSON><PERSON> kim", "join": "Tham gia", "notiUpgradeMsgJoin": "<PERSON><PERSON> một <PERSON>h<PERSON>ch hàng vừa tham gia cửa hàng. <PERSON><PERSON> nhiên gói của bạn đã vượt hạn mức cho phép.", "guestDelete": "Đã xóa", "statistics": "<PERSON><PERSON><PERSON><PERSON> kê", "unlimited": "<PERSON><PERSON><PERSON>ng gi<PERSON>i hạn", "participant": "<PERSON><PERSON><PERSON><PERSON> tham gia", "campaigns": "<PERSON><PERSON><PERSON>", "credit": "<PERSON>", "codesScannedUsage": "Số mã đã quét", "redeemRewardsUsage": "Ưu đãi đã đổi", "newCustomersUsage": "<PERSON><PERSON><PERSON><PERSON> hàng mới", "returningCustomersUsage": "<PERSON><PERSON><PERSON><PERSON> hàng quay lại", "week": "<PERSON><PERSON><PERSON>", "month": "<PERSON><PERSON><PERSON><PERSON>", "curentMonth": "<PERSON><PERSON><PERSON><PERSON> hi<PERSON> tại", "pendingApprovals": "<PERSON><PERSON> chờ phê du<PERSON>t{total}", "@pendingApprovals": {"description": "total", "placeholders": {"total": {"type": "String"}}}, "approve": "Xác nhận", "reject": "<PERSON><PERSON> chối", "pendingApprovalsAlertMsg": "Bạn có {quantity} kh<PERSON>ch hàng hoặc giao dịch đang chờ phê duyệt. Vui lòng xem xét và phê duyệt nếu cần.", "@pendingApprovalsAlertMsg": {"description": "quantity", "placeholders": {"quantity": {"type": "String"}}}, "pendingApprovalsEmpty": "<PERSON><PERSON><PERSON><PERSON> có dữ liệu để hiển thị ngay bây giờ!", "skip": "Bỏ qua", "addAnnouncement": "<PERSON><PERSON><PERSON><PERSON> thông báo", "history": "<PERSON><PERSON><PERSON> s<PERSON>", "customersEmpty": "<PERSON><PERSON><PERSON><PERSON> có dữ liệu để hiển thị ngay bây giờ!", "customersNotFound": "<PERSON>h<PERSON>ng có khách hàng nào phù hợp với tìm kiếm!", "loading": "<PERSON><PERSON> t<PERSON>", "boTitle": "Bạn muốn nhiều tính năng hơn?", "boSubtitle": "<PERSON><PERSON><PERSON> c<PERSON><PERSON> lý để sử dụng nhiều tính năng dành riêng cho chủ cửa hàng.", "accessNow": "<PERSON><PERSON><PERSON> c<PERSON><PERSON> ngay", "titleNotNetwork": "<PERSON><PERSON><PERSON><PERSON> có kết nối internet. Đang thử lại...", "titleNetwork": "<PERSON><PERSON> kết nối.", "somethingWentWrong": "Đ<PERSON> xảy ra lỗi.", "currentcustomers": "<PERSON><PERSON><PERSON><PERSON>", "rewardsProgram": "<PERSON><PERSON><PERSON><PERSON> trình <PERSON>u đãi", "forFree": "<PERSON><PERSON><PERSON> phí", "quickStartGuide": "Hướng dẫn nhanh", "setUpBusinessProfile": "<PERSON><PERSON><PERSON><PERSON> lậ<PERSON> hồ sơ cửa hàng", "setUpRewards": "<PERSON><PERSON><PERSON><PERSON> l<PERSON><PERSON> đãi", "setUpPrinterAndLabel": "<PERSON><PERSON><PERSON><PERSON> l<PERSON>p <PERSON> in & Nhãn", "referralProgramQuickSetUp": "<PERSON><PERSON><PERSON><PERSON> trình giới thiệu", "complete": "<PERSON><PERSON><PERSON> th<PERSON>", "kindlyCreateAtLeastOneRewardForCustomerToRedeem": "<PERSON><PERSON> lòng tạo ít nhất một ưu đãi để khách hàng có thể đổi điểm.", "youCanReferToSomeSampleRewardsSuggestedByAITecnology": "<PERSON>ạn có thể tham khảo một số mẫu ưu đãi được đề xuất bởi công nghệ AI.", "aIGenerate": "Tạo bằng AI", "generate": "<PERSON><PERSON><PERSON> đãi", "newRewards": "<PERSON><PERSON><PERSON><PERSON> đãi", "suggetsSetupRewardsDescription": "<PERSON><PERSON> xuất 10 ưu đãi như Phở, <PERSON><PERSON>,… với số điểm dao động từ 10-100 điểm.", "submit": "<PERSON><PERSON><PERSON>", "inputYourReward": "<PERSON><PERSON><PERSON><PERSON> <PERSON> đãi của bạn", "exampleReward": "VD: Ưu đãi", "joinRewards": "Tham gia", "programForFree": "<PERSON><PERSON> đãi miễn phí", "billingPaymentMethod": "<PERSON><PERSON> toán", "currentCredit": "<PERSON> đang có", "addOns": "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON> b<PERSON> sung", "manager": "<PERSON><PERSON><PERSON><PERSON> lý", "invoices": "<PERSON><PERSON><PERSON>", "invoicesSubtitle": "<PERSON><PERSON><PERSON><PERSON> lý hoá đơn thanh toán của bạn", "upcoming": "<PERSON><PERSON> <PERSON>h toán mới", "viewInvoice": "<PERSON><PERSON> h<PERSON> đ<PERSON>n", "paymentMethods": "<PERSON><PERSON><PERSON><PERSON> thức thanh toán", "updateCard": "<PERSON><PERSON><PERSON> nhật thẻ", "businessAddress": "<PERSON><PERSON><PERSON> chỉ c<PERSON>a hàng", "updateAddress": "<PERSON><PERSON><PERSON> nh<PERSON>t địa chỉ", "additionalInformation": "Thông tin thêm", "addInformation": "<PERSON><PERSON><PERSON><PERSON> thông tin", "addInformationSubtitle": "VAT id và thông tin khác hiển thị trên hóa đơn của bạn.", "paid": "<PERSON><PERSON> thanh toán", "expirationMonth": "<PERSON><PERSON><PERSON><PERSON> hế<PERSON> hạn", "expirationYear": "<PERSON><PERSON><PERSON> hế<PERSON> hạn", "expiration": "<PERSON><PERSON><PERSON>", "cvc": "CVC", "cardNumber": "Số thẻ", "cardNumberRequired": "Số thẻ là bắt buộc", "nameOnCard": "Tên trên thẻ", "nameOnCardRequired": "<PERSON>ên trên thẻ là bắt buộc", "vatGSTID": "VAT/GST ID", "informationToShowOnYourInvoices": "Thông tin hiển thị trên hóa đơn của bạn", "touchHereToAccessMenu": "<PERSON>ạm vào đây để vào Menu", "touchHereToSetUpProfile": "<PERSON>ạm vào đây để chỉnh sửa hồ sơ", "touchHereToSelectSettings": "Chạm vào đây để xem các cài đặt", "touchHereToSetUpReferralProgram": "<PERSON>ạm vào đây để cài đặt chương trình giới thiệu.", "touchHereToInstalllPrinterAndEditLabel": "Chạm vào đây để cài đặt máy in và chỉnh sửa tem nhãn.", "qtyPrintQueue": "Số lượng", "plan": "<PERSON><PERSON><PERSON>", "free": "<PERSON><PERSON><PERSON> phí", "monthly": "<PERSON><PERSON><PERSON>g", "yearly": "<PERSON><PERSON><PERSON>", "mostPopular": "<PERSON><PERSON> biến nhất", "currentPlan": "<PERSON><PERSON><PERSON> hi<PERSON> tại", "contact": "<PERSON><PERSON><PERSON>", "freeSubtitle": "<PERSON><PERSON><PERSON> phí trọn đời", "priceMonthlySubtitle": "mỗi tháng", "priceYearlySubtitle": "mỗi tháng khi thanh toán hàng năm", "titleSelectedPlan": "Bạn có chắc chắn muốn nâng cấp lên gói {plan} này không?", "@titleSelectedPlan": {"description": "plan", "placeholders": {"plan": {"type": "String"}}}, "firstWelcome": "Xin chào!", "firstSolution": "<PERSON><PERSON><PERSON><PERSON> p<PERSON>p", "firstToolsIntegration": "<PERSON><PERSON><PERSON> cụ tích hợp", "firstDesStep1": "<PERSON><PERSON><PERSON><PERSON> pháp để thúc đẩy sự trung thành của khách hàng và tăng doanh số qua các chương trình khách hàng thân thiết.", "firstDesStep2": "Tặng điểm cho khách hàng khi họ sử dụng dịch vụ của cửa hàng và dễ dàng quy đổi số điểm đã tích lũy để nhận được sản phẩm miễn phí hoặc giảm giá.", "firstDisplaysPrintingOptionForQRCodes": "<PERSON><PERSON><PERSON> chọn hiển thị và in mã QR", "firstCampaignMessage": "<PERSON>n chiến dịch", "firstSignage": "Các mẫu in ấn", "setAsdeDault": "Đặt làm mặc định", "expires": "Hết hiệu lực", "expiresTitle": "Hết hiệu lực", "addAPayment": "<PERSON><PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON><PERSON> thức thanh toán", "viewAll": "<PERSON><PERSON> t<PERSON>t cả", "invoiceEmptyTitle": "<PERSON><PERSON><PERSON><PERSON> có hóa đơn", "creditsPurchased": "<PERSON><PERSON> xu", "planUpgradedGoldMonthly": "<PERSON><PERSON> to<PERSON> hàng tháng - <PERSON><PERSON><PERSON>", "planUpgradedGoldYearly": "<PERSON><PERSON> to<PERSON> hàng năm - <PERSON><PERSON><PERSON>", "planUpgradedPlatinumMonthly": "<PERSON><PERSON> <PERSON><PERSON> hàng thán<PERSON> - <PERSON><PERSON><PERSON>", "planUpgradedPlatinumYearly": "<PERSON><PERSON> <PERSON><PERSON> hàng n<PERSON> - <PERSON><PERSON><PERSON>", "removeCard": "Xóa thẻ", "confirmRemoveCardMsg": "Bạn có chắc chắn muốn xóa thẻ không?", "monthRequired": "<PERSON><PERSON><PERSON><PERSON> là b<PERSON> bu<PERSON>c", "yearRequired": "<PERSON><PERSON><PERSON> là b<PERSON> buộc", "streetRequired": "<PERSON><PERSON><PERSON> đ<PERSON><PERSON><PERSON> là bắt buộc", "cityRequired": "<PERSON><PERSON><PERSON><PERSON> phố/Tỉnh là bắt buộc", "billingAddress": "<PERSON><PERSON><PERSON> chỉ thanh toán", "addPaymentMethod": "<PERSON><PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON><PERSON> thức thanh toán", "cvcRequired": "CVC là bắt buộc", "expirationRequired": "<PERSON><PERSON><PERSON> hạn là b<PERSON><PERSON> buộc", "expirationInvalid": "<PERSON><PERSON><PERSON> hạn không hợp lệ", "thereAreNoActiveAnnouncements": "<PERSON><PERSON><PERSON><PERSON> có thông báo nào đang hoạt động", "thereAreNoInactiveAnnouncements": "<PERSON>hông có thông báo nào ở trạng thái không hoạt động", "thereAreNoActiveRewards": "<PERSON><PERSON><PERSON><PERSON> có ưu đãi nào đang hoạt động", "thereAreNoInactiveRewards": "Không có ưu đãi nào ở trạng thái không hoạt động", "pleaseCreateAtLeastOneRewardToDisplayOnTheTemplate": "<PERSON><PERSON> lòng tạo ít nhất một ưu đãi để hiển thị trên mẫu", "address": "Địa chỉ", "addressRequired": "Địa chỉ là bắt buộc", "addCardSuccess": "Thẻ của bạn đã được thêm thành công", "deleteCardSuccess": "Thẻ của bạn đã được xoá thành công", "paymentEmpty": "<PERSON><PERSON><PERSON> có ph<PERSON><PERSON><PERSON> thức thanh toán nào trong tài khoản của bạn.", "easyAndFree": "<PERSON><PERSON> & <PERSON><PERSON><PERSON> phí", "theEasiestWayToExciteCustomers": "<PERSON><PERSON><PERSON> đơn giản để kích thích sự hứng thú của khách hàng và duy trì sự trung thành của họ là sử dụng hệ thống điểm thưởng - hoàn toàn miễn phí.", "anytimeAnyWhere": "<PERSON><PERSON><PERSON> l<PERSON>c mọi n<PERSON>i", "sendCustomersMessagesAndExclusiveOffers": "G<PERSON>i tin nhắn và các ưu đãi đặc biệt cho khách hàng. Điều này giúp duy trì sự kết nối và tạo thêm động lực cho khách hàng quay lại!", "getYourBusinessUpAndRunningIn15Minutes": "<PERSON><PERSON><PERSON><PERSON> lập cửa hàng của bạn chỉ trong 15 phút!", "letGo": "<PERSON><PERSON><PERSON> đ<PERSON>u", "noData": "<PERSON><PERSON><PERSON><PERSON> có dữ liệu", "confirmRejectMsg": "Bạn có chắc chắn muốn từ chối không?", "confirmApproveMsg": "Bạn có chắc chắn muốn chấp nhận không?", "updateApp": "<PERSON><PERSON><PERSON> nhật <PERSON>ng dụng?", "whatIsNews": "Thông tin gì mới?", "updateAppTitle": "M<PERSON>t phiên bản mới của {appName} đã có! Phiê<PERSON> bản {storeVersion} hiện đã có sẵn, phiên bản hiện tại của bạn là {currentVersion}", "@updateAppTitle": {"description": "and email", "placeholders": {"appName": {"type": "String"}, "storeVersion": {"type": "String"}, "currentVersion": {"type": "String"}}}, "updateNow": "<PERSON><PERSON><PERSON> nh<PERSON>t b<PERSON>y giờ", "adjustCoordinates": "Điều chỉnh tọa độ", "addCoordinates": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>a độ", "countryRequired": "Quốc gia là b<PERSON>t buộc", "selectCurrency": "<PERSON><PERSON><PERSON> lo<PERSON>i tiền tệ", "selectTimeZone": "<PERSON><PERSON><PERSON> múi giờ", "approveTotalMsg": "<PERSON><PERSON> lượng khách hàng chấp thuận ({noAllow}) không thể vượt quá số lượng khách hàng tối đa ({noMax})", "@approveTotalMsg": {"description": "", "placeholders": {"noAllow": {"type": "int"}, "noMax": {"type": "int"}}}, "approveAll": "<PERSON><PERSON><PERSON>n tất cả", "rejectAll": "<PERSON>ừ chối tất cả", "confirmRejectAllMsg": "Bạn có chắc chắn muốn từ chối tất cả không?", "confirmApproveAllMsg": "Bạn có chắc chắn muốn chấp nhận tất cả không?", "enterAddress": "<PERSON><PERSON><PERSON><PERSON> địa chỉ", "security": "<PERSON><PERSON><PERSON>", "confirmPasswordDoesNotMatch": "<PERSON><PERSON><PERSON> nh<PERSON>n mật khẩu không khớp", "setPasswordBusiness": "Đặt mật khẩu", "deducted": "<PERSON><PERSON><PERSON><PERSON> trừ điểm", "deductedStatus": "<PERSON><PERSON><PERSON><PERSON> trừ", "deduct": "Trừ", "reason": "<PERSON><PERSON> <PERSON> (không bắt buộc)", "reasonLabel": "Lý do", "thePointsHasBeenDeduct": "<PERSON><PERSON> trừ {points}", "@thePointsHasBeenDeduct": {"description": "points", "placeholders": {"points": {"type": "String"}}}, "deductPoints": "<PERSON><PERSON><PERSON> điểm", "has": "có", "have": "có", "offerPeriod": "<PERSON><PERSON><PERSON><PERSON> gian hi<PERSON> l<PERSON>c", "offerDetail": "<PERSON> ti<PERSON>", "offerRedeem": "Vouchers", "offerRedeemSuccess": "Đổi mã voucher thành công.", "offerRedeemError": "Đổi mã voucher không thành công.", "offerType": "Đổi voucher", "offerError": "<PERSON><PERSON><PERSON><PERSON> thể áp dụng voucher.", "offers": "Vouchers", "printError": "Trong quá trình in, một số nhãn không được in thành công. Bạn có muốn tiếp tục in không?", "draftOfferStatus": "<PERSON><PERSON><PERSON>", "activeOfferStatus": "<PERSON><PERSON><PERSON>", "runningOfferStatus": "<PERSON><PERSON>", "offersEmpty": "Không có voucher nào", "startDate": "<PERSON><PERSON><PERSON> b<PERSON><PERSON> đ<PERSON>u", "endDate": "<PERSON><PERSON><PERSON> k<PERSON> th<PERSON>c", "status": "<PERSON><PERSON><PERSON><PERSON> thái", "maxQuantity": "<PERSON><PERSON> l<PERSON><PERSON> tối đa", "maxPerUser": "G<PERSON><PERSON>i hạn mỗi người dùng", "private": "<PERSON><PERSON><PERSON><PERSON> tư", "public": "<PERSON><PERSON><PERSON> khai", "performance": "<PERSON><PERSON><PERSON>", "remaining": "<PERSON>òn lại", "claimedOffer": "Đã nhận", "offer": "Voucher", "redeemedOffer": "<PERSON><PERSON> đổi", "givePoints": "Tặng điểm", "from": "Từ", "to": "<PERSON><PERSON><PERSON>", "redeemOffer": "Đổi voucher?", "offersToRedeem": "<PERSON><PERSON><PERSON> không có voucher để đổi", "tags": "Gắn thẻ", "internalIdentifiersThatYouCanUseToCategorizeYourCustomers": "<PERSON><PERSON><PERSON> trị nhận dạng nội bộ mà bạn có thể sử dụng để phân loại khách hàng của mình.", "clearAll": "Xoá lựa chọn", "enterTagName": "<PERSON><PERSON><PERSON><PERSON> thẻ", "create": "Tạo thẻ", "theNameMustNotExceed128Characters": "Thẻ tag không đư<PERSON><PERSON> vư<PERSON><PERSON> quá 128 ký tự.", "offerCode": "Mã voucher", "delivered": "Đ<PERSON> gửi", "available": "<PERSON><PERSON> sẵn", "giveOffer": "Tặng voucher", "giveTiltle": "Tặng", "claimOfferSuccessful": "Thêm voucher thành công!", "scheduled": "<PERSON><PERSON><PERSON>", "offersToClaim": "<PERSON><PERSON>n tại không có voucher nào", "paused": "<PERSON><PERSON><PERSON>", "completed": "<PERSON><PERSON> hoàn thành", "dateClaimTitle": "<PERSON>h<PERSON><PERSON> gian khách hàng có thể nhận mã voucher", "dateRedeemTitle": "<PERSON>hời gian kh<PERSON>ch hàng có thể đổi mã voucher", "tagsTitle": "Thẻ", "enterTitle": "<PERSON><PERSON><PERSON><PERSON> tiêu đề", "setDefault": "Đặt nhãn mặc định", "deleteLabel": "Xoá n<PERSON>ãn", "titleLabelTemplate": "<PERSON><PERSON><PERSON><PERSON> đề nhãn", "validityPeriod": "<PERSON><PERSON><PERSON><PERSON> gian nhận:", "labelsEmpty": "<PERSON>hông có nhãn nào!", "labelOfferTitleText": "QUÉT MÃ QR ĐỂ NHẬN VOUCHER", "labelOfferText": "<PERSON><PERSON><PERSON>", "labelOfferTitle": "[Ti<PERSON><PERSON> đề]", "labelOfferCode": "[Mã voucher]", "labelOfferDate": "[<PERSON><PERSON><PERSON> <PERSON><PERSON> voucher]", "labelPoints": "[<PERSON><PERSON><PERSON><PERSON>]", "labelscanQR": "QUÉT QR", "labelsToGet": "ĐỂ NHẬN", "addLabel": "<PERSON><PERSON><PERSON><PERSON>", "printingStarting": "<PERSON><PERSON> b<PERSON><PERSON> đầu in", "titleDupLabel": "Mua 1 tặng 1", "codeDupLabel": "MUA1TANG1", "yourPhoneNumber": "<PERSON><PERSON> điện tho<PERSON>i của bạn", "loginCustomerTitle": "<PERSON><PERSON> lòng nhập mật kh<PERSON>u của bạn", "loginCustomerSubTitle": "Bạn đã có tài khoản. Bạn có muốn sử dụng tài khoản này để đăng ký cửa hàng không?", "stickyQRApp": "STICKYQR", "downloadTheSignage": "<PERSON><PERSON><PERSON> về <PERSON>ng dụng", "joinRewardProgram": "Tham gia chương trình <PERSON>u đãi", "buyCredit": "<PERSON><PERSON><PERSON><PERSON> l<PERSON>", "textCredit": "<PERSON>", "buy": "<PERSON><PERSON>", "currentlyBalance": "<PERSON> tại", "offerCreateTitle": "Tạo voucher", "offerEditTitle": "Chỉnh sửa voucher", "overview": "<PERSON><PERSON><PERSON> quan", "offerName": "<PERSON><PERSON>n voucher", "offerNameSubTitle": "Đặt tên nội bộ cho ưu đãi của bạn để giúp sắp xếp và định vị ưu đãi đó dễ dàng trong tài khoản của bạn.", "offerTitle": "Ti<PERSON>u đề voucher", "offerContent": "<PERSON>ội dung voucher", "offerContentSubtitle": "Đặt nội dung tin nhắn mà khách hàng của bạn sẽ nhận được.", "addOfferContent": "<PERSON><PERSON><PERSON><PERSON> nội dung voucher", "offerHeader": "<PERSON><PERSON><PERSON><PERSON> (<PERSON><PERSON><PERSON>)", "offerImages": "<PERSON><PERSON><PERSON> (<PERSON><PERSON><PERSON>)", "offerHeaderSubtitle": "<PERSON><PERSON> dụng hình <PERSON>nh làm tiêu đề", "offerStartDate": "<PERSON><PERSON><PERSON> b<PERSON><PERSON> đ<PERSON>u", "offerEndDate": "<PERSON><PERSON><PERSON> (<PERSON><PERSON><PERSON>)", "datePicker": "<PERSON><PERSON><PERSON>", "timePicker": "<PERSON><PERSON><PERSON> g<PERSON>", "maximumQuantity": "<PERSON><PERSON> l<PERSON><PERSON> tối đa", "maximumQuantitySub": "Voucher tối đa cho tất cả người dùng", "maximumPerUser": "T<PERSON>i đa cho mỗi người dùng", "maximumPerUserSub": "Số lượng voucher tối đa mà người dùng có thể nhận được", "addTag": "Gắn thẻ", "saveAndExit": "Lưu & Thoát", "saveAndStart": "Lưu & Bắt đầu", "customerPayment": "<PERSON><PERSON> toán", "enterContent": "<PERSON><PERSON><PERSON><PERSON> n<PERSON> dung", "dates": "<PERSON><PERSON><PERSON>", "noExpiration": "<PERSON><PERSON> thời hạn", "performaceOffer": "<PERSON><PERSON><PERSON> voucher", "showQR": "<PERSON><PERSON><PERSON> thị mã QR", "printLabel": "In nhãn", "titleOfferCode": "<PERSON><PERSON><PERSON> mã QR để nhận voucher.", "item": "<PERSON><PERSON><PERSON>", "currentlyCredit": "<PERSON><PERSON> dụng hiện tại", "payment": "<PERSON><PERSON> toán", "addNote": "<PERSON><PERSON><PERSON><PERSON> ghi chú", "deleteNote": "Xoá ghi chú", "sendPaymentLink": "<PERSON><PERSON><PERSON> liên kết thanh toán", "scanQrCodeToPay": "<PERSON><PERSON>t mã để thanh toán", "note": "<PERSON><PERSON><PERSON>", "pleaseEnterTheAmount": "<PERSON><PERSON> lòng nh<PERSON>p số tiền", "confirmPhoneNumber": "<PERSON><PERSON><PERSON> nhận số điện thoại", "billAmount": "<PERSON><PERSON><PERSON><PERSON> số tiền", "pleaseEnterAValidAmount": "<PERSON><PERSON> lòng nhập số tiền hợp lệ.", "imagesOffer": "<PERSON><PERSON><PERSON>", "permissionLocalNetworkTitle": "Ứng dụng cần được cấp quyền mạng cục bộ, vui lòng cung cấp quyền.", "help": "Hỗ trợ", "printerHelpStep1": "<PERSON><PERSON><PERSON><PERSON> bị cần kết nối cùng một mạng cục bộ với máy in.", "printerHelpStep2": "<PERSON><PERSON> cấp quyền mạng cục bộ cho thiết bị.", "printerHelpStep3": "<PERSON><PERSON> thể lấy và nhập địa chỉ IP của máy in bằng cách thủ công.", "printerHelpStep31": "Ấn giữ đồng thời 2 nút \"Feed\" và \"Cancel\" → đ<PERSON><PERSON> nháy thì thả tay → địa chỉ IP ở hàng \"IP ADDRESS\".", "printerHelpStep32": "Tắt nguồn → ấn giữ nút \"Feed\" → bật nguồn → đèn báo đổi màu tím thì thả tay → địa chỉ IP ở hàng \"DEFAULT GATEWAY\".", "printerHelpStep4": "In thử, nếu nhãn chưa căn chỉnh đúng thì ấn nút \"điều chỉnh\" để căn chỉnh lại.", "setting": "<PERSON><PERSON><PERSON><PERSON> l<PERSON>", "amount": "<PERSON><PERSON> tiền", "tip": "Tip", "paymentMethod": "<PERSON><PERSON><PERSON><PERSON> thức thanh toán", "card": "Thẻ", "msgTooltipCard": "Thanh toán bằng thẻ", "msgTooltipGG": "Thanh toán qua Google Pay", "msgTooltipApple": "<PERSON>h toán qua Apple Pay", "msgTooltipVisaCard": "Thanh toán bằng thẻ Visa", "msgTooltipMasterCard": "<PERSON><PERSON> toán bằng thẻ Master", "msgTooltipAmexCard": "Thanh toán bằng thẻ Amex", "pointClaims": "<PERSON><PERSON><PERSON> c<PERSON>u nhận điểm", "pointClaimsSubTitle": "<PERSON><PERSON><PERSON> có khách hàng yêu cầu nhận điểm!", "totalPointClaims": "{data} khách hàng", "@totalPointClaims": {"description": "data point claim", "placeholders": {"data": {"type": "int"}}}, "givePointClaimTitle": "Tặng", "signageGetStarted": "Tham gia", "signagePointClaims": "<PERSON><PERSON><PERSON><PERSON> điểm", "pointClaimsCounterCard": "Mẫu in ấn", "pointClaimsTitleContent": "Mẫu {number}", "@pointClaimsTitleContent": {"description": "", "placeholders": {"number": {"type": "String"}}}, "scanToClaimYourPoints": "<PERSON><PERSON>t mã\nđể yêu cầu\nnhận điểm", "rejectAllPointClaimTitle": "<PERSON>ừ chối tất cả", "rollIn": "Roll in", "confirmRejectAllPointLaimsMsg": "Bạn có chắc chắn muốn từ chối tất cả?", "pointClaimNotif": "{name} vừa gửi một yêu cầu nhận điểm!", "@pointClaimNotif": {"description": "", "placeholders": {"name": {"type": "String"}}}, "pointClaimGiveConfirm": "<PERSON><PERSON> lòng x<PERSON>c nhận {points} điểm sẽ được tặng.", "@pointClaimGiveConfirm": {"description": "", "placeholders": {"points": {"type": "String"}}}, "features": "<PERSON><PERSON><PERSON>", "featuresPointClaims": "<PERSON><PERSON><PERSON> c<PERSON>u nhận điểm", "featureNew": "<PERSON><PERSON><PERSON>", "enabledPointClaims": "<PERSON><PERSON><PERSON> đ<PERSON>", "disabledPointClaims": "<PERSON><PERSON> hi<PERSON> h<PERSON>a", "allowUsersToScanQrCodesAtYourBusiness": "<PERSON> phép khách hàng tạo yêu cầu về điểm bằng cách quét mã QR nhận điểm từ cửa hàng.", "programToAccumulatePointsForCustomers": "Tặng điểm khi giới thiệu bạn bè đến cửa hàng của bạn.", "businessPhone": "<PERSON><PERSON> đi<PERSON>n tho<PERSON>i", "businessPhoneRequired": "<PERSON><PERSON> điện thoại là bắt buộc", "referrals": "<PERSON><PERSON><PERSON><PERSON> thi<PERSON>u", "engagement": "<PERSON><PERSON><PERSON><PERSON> t<PERSON> tác", "soundSettings": "<PERSON><PERSON>", "enableSoundPointClaims": "<PERSON><PERSON><PERSON> thông báo bằng âm thanh khi yêu cầu nhận điểm mới", "beep3Times": "Bíp 3 lần", "continuousBeeping": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON> liên tục cho đến khi được xác nhận", "notificationSound": "<PERSON><PERSON> thanh thông báo", "tink": "Tink", "pop": "Pop", "beep": "Link", "offerClaimed": "<PERSON><PERSON> nhận voucher", "showInAppOnly": "Chỉ dành cho <PERSON>ng dụng (StickyQR)", "appOnly": "Dành cho app", "explore": "<PERSON><PERSON><PERSON> c<PERSON><PERSON> ngay", "homeCallButton": "Call <PERSON>ton", "homeClaimPoints": "<PERSON><PERSON><PERSON> c<PERSON>u nhận điểm", "homeReject": "<PERSON><PERSON> chối", "homeGive": "Tặng", "homeEnterPoints": "<PERSON><PERSON><PERSON><PERSON> đi<PERSON>", "homeMarkDoneCompleted": "<PERSON><PERSON> hoàn thành tất cả", "homeShowAllRewards": "<PERSON><PERSON><PERSON> thị tất cả ưu đãi", "homeShowLess": "<PERSON><PERSON>", "homeCloseRequestAfter": "<PERSON><PERSON><PERSON> yêu cầu sau", "homeUndo": "Huỷ", "homeCloseOut": "<PERSON><PERSON><PERSON> l<PERSON>", "homeRedeemPoints": "<PERSON><PERSON><PERSON> ưu đãi", "homeThereAreNoRequests": "<PERSON><PERSON><PERSON><PERSON> có yêu cầu nào", "homeCallBtnPleaseSelectARewardToRedeem": "<PERSON><PERSON> lòng chọn ưu đãi bạn muốn đổi", "homeCallBtnCloseOutInactiveTable": "<PERSON><PERSON><PERSON> c<PERSON>c bàn không hoạt động", "homeCallBtnThereAreNoRequestPendingAtThisTime": "<PERSON><PERSON><PERSON> có yêu cầu nào đang chờ xử lý vào thời điểm này", "callButton": "Call <PERSON>ton", "callButtonTitle": "Call button", "tables": "Bàn", "service": "<PERSON><PERSON><PERSON> v<PERSON>", "callButtonSubTitle": "<PERSON><PERSON><PERSON> hợ<PERSON> Call Button để hỗ trợ khách hàng <PERSON> chón<PERSON>, ch<PERSON><PERSON><PERSON> nghi<PERSON>, đ<PERSON><PERSON> bả<PERSON> bạn luôn đáp <PERSON>ng kịp thời những yêu cầu của khách hàng.", "request": "<PERSON><PERSON><PERSON> c<PERSON>", "action": "<PERSON><PERSON><PERSON>", "externalLink": "<PERSON><PERSON><PERSON> kết bên ngo<PERSON>i", "link": "<PERSON><PERSON><PERSON>", "scanQRTitle": "<PERSON><PERSON>t QR", "addNewSection": "<PERSON><PERSON><PERSON><PERSON>", "sectionName": "<PERSON><PERSON><PERSON>", "renameSectionTable": "<PERSON><PERSON><PERSON> tên", "printAllTablesQR": "In mã QR cho tất cả các bàn", "addANewTable": "<PERSON><PERSON><PERSON><PERSON>n", "tablesService": "<PERSON><PERSON><PERSON> v<PERSON> bàn", "setTableService": "Đặt tất cả dịch vụ cho{table}bàn", "@setTableService": {"description": "", "placeholders": {"table": {"type": "String"}}}, "renameService": "<PERSON><PERSON><PERSON> tên", "addANewService": "<PERSON><PERSON><PERSON><PERSON> v<PERSON>", "editANewService": "<PERSON><PERSON><PERSON> d<PERSON> vụ", "serviceName": "<PERSON><PERSON><PERSON> v<PERSON>", "cbSectionNameRequired": "<PERSON><PERSON><PERSON> khu v<PERSON><PERSON> là bắt buộc", "newSection": "<PERSON><PERSON><PERSON><PERSON> nhóm mới", "qrCode": "Mã QR", "redeemPoints": "<PERSON><PERSON><PERSON> điểm", "survey": "S<PERSON> kh<PERSON>o s<PERSON>t", "qrContent": "Nội dung QR", "enterServiceName": "<PERSON><PERSON><PERSON><PERSON> tên d<PERSON> vụ", "suggest": "G<PERSON><PERSON> ý", "prefix": "<PERSON><PERSON><PERSON><PERSON> tố", "numberOfTables": "<PERSON><PERSON> l<PERSON> bàn", "none": "<PERSON><PERSON><PERSON><PERSON> có", "serviceTitle": "{total} <PERSON><PERSON><PERSON> vụ", "@serviceTitle": {"description": "", "placeholders": {"total": {"type": "String"}}}, "cbcounterCardTitleContent": "Thẻ để bàn {number}", "@cbcounterCardTitleContent": {"description": "", "placeholders": {"number": {"type": "String"}}}, "subTitle": "<PERSON><PERSON> đề", "callButtonSignageTitle": "CALL BUTTON", "callButtonSignageSubTitle": "(tại sao phải chờ đợi?)", "callButtonSignageDescription": "Chỉ cần quét mã và gửi yêu cầu. Từ ho<PERSON> đơn, nư<PERSON><PERSON> uố<PERSON>, gia vị hay đồ dùng sẽ được đáp ứng tức thì. Chúng tôi sẵn lòng phục vụ.", "fontSize": "Cỡ chữ", "spacing": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>ch", "anchorTextBox": "<PERSON> h<PERSON><PERSON> v<PERSON><PERSON> b<PERSON>n", "tableSubtitle": "<PERSON><PERSON><PERSON><PERSON> lý bàn và khu vự<PERSON>, g<PERSON> dịch vụ cho bàn, in mã QR.", "serviceSubtitle": "<PERSON><PERSON><PERSON><PERSON> lý các dịch vụ nơi khách hàng và cửa hàng tương tác.", "deleteSection": "Xóa khu vực", "editTable": "Sửa thông tin bàn & d<PERSON><PERSON> vụ", "tableService": "<PERSON><PERSON><PERSON> v<PERSON> bàn", "iconRequired": "<PERSON><PERSON><PERSON><PERSON> tư<PERSON><PERSON> là bắt buộc", "linkRequired": "<PERSON><PERSON><PERSON> kết là b<PERSON><PERSON> bu<PERSON>c", "qrCodeRequired": "Mã QR là bắt buộc", "beverage": "<PERSON><PERSON>", "food": "<PERSON><PERSON> ăn", "fruit": "<PERSON><PERSON> quả", "other": "K<PERSON><PERSON><PERSON>", "seasonings": "Gia vị", "sweets": "<PERSON><PERSON>", "utensils": "<PERSON><PERSON> dùng", "vegetables": "<PERSON><PERSON>", "customizeTitle": "<PERSON><PERSON><PERSON> chỉnh", "soundSettingSub": "<PERSON><PERSON> thanh để thông báo.", "thankYouSettings": "<PERSON><PERSON> c<PERSON>", "thankYouSettingsSub": "<PERSON><PERSON><PERSON> thị thông báo sau khi đóng bàn.", "deleteSectionTitle": "Bạn có muốn xoá \"{section}\" không?", "@deleteSectionTitle": {"description": "section", "placeholders": {"section": {"type": "String"}}}, "noteDeleteTable": "Lưu ý: <PERSON><PERSON><PERSON> xoá khu vực này thì toàn bộ bàn sẽ bị xoá.", "noteDeleteService": "Lưu ý: <PERSON><PERSON><PERSON> xóa phần này thì toàn bộ dịch vụ sẽ bị xóa.", "numberOfTablesRequired": "Số lượng bàn là bắt buộc", "tablesServiceRequired": "<PERSON><PERSON><PERSON> vụ cho bàn là bắt buộc", "cbSelectAll": "<PERSON><PERSON><PERSON> tất cả", "renameSectionTitle": "Đ<PERSON><PERSON> tên khu vực", "table": "Bàn", "hintAddSection": "<PERSON>í dụ: Tầng 1", "linkInvalid": "<PERSON><PERSON><PERSON> kết không h<PERSON>p lệ", "cbEnableSound": "<PERSON><PERSON><PERSON> thông báo âm thanh cho yêu cầu mới", "enterYourMessage": "<PERSON><PERSON><PERSON><PERSON> tin nhắn của bạn...", "callToActionButtonTitle": "<PERSON><PERSON><PERSON>", "buttonTitle": "<PERSON>i<PERSON><PERSON> đề nút", "thankYouTitle": "<PERSON><PERSON><PERSON>n!", "thanksMsgTitle": "<PERSON><PERSON>g tôi xin chân thành cảm ơn sự hợp tác của bạn ngày hôm nay và hy vọng có thể sớm phục vụ bạn lần nữa", "goToHomePage": "<PERSON><PERSON><PERSON> trang chủ", "buttonTitleRequired": "Tiêu đề nút là bắt buộc", "beta": "<PERSON><PERSON><PERSON>", "serviceSectionName": "<PERSON><PERSON><PERSON>", "serviceSectionNameRequired": "<PERSON><PERSON><PERSON> n<PERSON> là bắ<PERSON> bu<PERSON>c", "deleteServiceSection": "Xóa nhóm", "tableName": "<PERSON><PERSON><PERSON> b<PERSON>n", "assignedServices": "<PERSON><PERSON><PERSON> v<PERSON> ({total})", "@assignedServices": {"description": "total", "placeholders": {"total": {"type": "String"}}}, "autoServices": "<PERSON><PERSON> động thêm dịch vụ mới", "autoServicesSub": "<PERSON><PERSON><PERSON> dịch vụ mới sẽ tự động được thêm vào bàn này nếu đư<PERSON><PERSON> chọn", "untitleServiceGroup": "(<PERSON><PERSON><PERSON><PERSON> dịch vụ không có tiêu đề)", "untitleSectionTable": "(<PERSON><PERSON> vự<PERSON> không có tiêu đề)", "newGroup": "<PERSON>h<PERSON><PERSON> mới", "serviceGroupNameHint": "<PERSON><PERSON><PERSON> n<PERSON> d<PERSON> (t<PERSON><PERSON> ch<PERSON>n)", "manageTableServices": "<PERSON><PERSON><PERSON><PERSON> lý d<PERSON> v<PERSON> bàn", "common": "<PERSON>", "utensilsSeasonings": "Đồ dùng & Gia vị", "foodBeverages": "<PERSON><PERSON><PERSON><PERSON> p<PERSON> & <PERSON><PERSON>ng", "fruitsVegetables": "<PERSON>u củ quả", "chooseUserAction": "<PERSON><PERSON><PERSON> hành động của người dùng:", "requestSubtitle": "<PERSON><PERSON><PERSON><PERSON> dùng gửi yêu cầu, nh<PERSON> gọi thanh to<PERSON>,…", "actionSubtitle": "<PERSON><PERSON><PERSON><PERSON> dùng thực hiện các hành động như yêu cầu điểm,...", "externalLinkSubtitle": "<PERSON><PERSON><PERSON><PERSON> dùng nhấp chuột để truy cập liên kết bên ngoài.", "qrCodeService": "Mã QR", "qrCodeSubtitle": "<PERSON><PERSON><PERSON> thị mã QR để người dùng quét.", "assignThisService": "Chỉ định dịch vụ này cho các bàn?", "assignedTables": "<PERSON><PERSON><PERSON>", "templateCallButton": "Chọn Mẫu In", "businessLogoTitle": "Hiển thị logo c<PERSON><PERSON> hàng", "businessNameTitle": "<PERSON><PERSON><PERSON> thị tên c<PERSON>a hàng", "actionSelection": "<PERSON><PERSON><PERSON>", "actionSelectionSubtitle": "<PERSON><PERSON><PERSON> loại hành động mà người dùng có thể thực hiện", "chooseAnAction": "<PERSON><PERSON><PERSON> một hành động", "functionName": "<PERSON><PERSON><PERSON>", "actionSelectionRequired": "<PERSON><PERSON><PERSON> động là b<PERSON>t buộc", "learnMore": "<PERSON><PERSON><PERSON> hi<PERSON>u thêm", "pointClaimsCB": "<PERSON><PERSON><PERSON> c<PERSON>u nhận điểm", "redeemPointsCB": "<PERSON><PERSON><PERSON> ưu đãi", "tablesEmpty": "Ch<PERSON>a có bàn nào!", "staffNote": "<PERSON><PERSON><PERSON>", "staffTags": "Gắn thẻ", "staffAddTag": "<PERSON><PERSON><PERSON><PERSON>", "staffNoteContent1": "<PERSON><PERSON>ân viên của bạn sẽ đặt mật khẩu riêng của họ thông qua ứng dụng StickyQR Manager.", "staffNoteContent2": "<PERSON><PERSON><PERSON> nhân viên của bạn đã sử dụng hệ thống StickyQR trước đây, họ có thể nhập mật khẩu mà họ đã sử dụng trước đó.", "rolesPermissionsTitle": "<PERSON><PERSON><PERSON><PERSON> truy cập", "rolePermissionSubtitle": "<PERSON><PERSON><PERSON><PERSON> lậ<PERSON> chức vụ và phân quyền", "roleTitle": "<PERSON><PERSON><PERSON> v<PERSON>", "staffInternalIndentifiersThatYouCanUseToCategorizeYourStaff": "<PERSON><PERSON><PERSON> mã định danh nội bộ mà bạn có thể sử dụng để phân loại nhân viên của mình.", "staffInternalIndentifiersThatYouCanUseToCategorizeYourCustomer": "<PERSON><PERSON><PERSON> mã định danh nội bộ mà bạn có thể sử dụng để phân loại khách hàng của mình.", "staffRole": "<PERSON><PERSON><PERSON> v<PERSON>", "customerAddTags": "Gắn thẻ", "owner": "Ch<PERSON> sở hữu", "roleIsRequired": "<PERSON><PERSON><PERSON> v<PERSON> là b<PERSON> buộc", "general": "<PERSON>", "businessSettings": "<PERSON><PERSON><PERSON> đặt cửa hàng", "stickyPoints": "Sticky Points", "stickyVouchers": "Sticky Vouchers", "stickyCallButton": "<PERSON><PERSON>", "read": "Xem", "checkAll": "<PERSON><PERSON><PERSON> tất cả", "settingsPoints": "<PERSON><PERSON>i đặt điểm", "settingsVouchers": "Cài đặt Vouchers", "settingsCallButton": "<PERSON><PERSON><PERSON><PERSON> lý quyền của t<PERSON>h năng <PERSON> Button", "allow": "<PERSON> phép", "roleName": "<PERSON><PERSON><PERSON><PERSON> tên chứ<PERSON> v<PERSON>", "roleNameRequired": "<PERSON><PERSON><PERSON> v<PERSON> là bắ<PERSON> bu<PERSON>c", "warningDeleteRole": "<PERSON><PERSON><PERSON> vụ này hiện đang do người khác đảm nhận. <PERSON><PERSON>, vui lòng chỉ định một chức vụ khác.", "transferDelete": "Di chuyển và Xóa", "noRole": "<PERSON><PERSON><PERSON> không có quyền truy cập", "serviceTokens": "Service Tokens", "customersPayment": "<PERSON><PERSON> to<PERSON> của kh<PERSON>ch hàng", "paymentGateways": "<PERSON><PERSON><PERSON> thanh toán", "voidTransactions": "<PERSON><PERSON> hi<PERSON> giao d<PERSON>ch", "managerTablesServices": "<PERSON><PERSON><PERSON><PERSON> lý bàn và dịch vụ", "manageCustomersRequests": "<PERSON><PERSON><PERSON><PERSON> lý yêu cầu của kh<PERSON>ch hàng", "rolesPermissions": "<PERSON><PERSON><PERSON><PERSON> truy cập", "rolesPermissionsTransferEmpty": "<PERSON><PERSON><PERSON><PERSON> có chức vụ nào để chuyển.", "noteDeleteRole1": "Những người đang có chức vụ", "noteDeleteRole2": "sẽ đư<PERSON><PERSON> chuyển qua chức vụ", "noteRole": "<PERSON><PERSON><PERSON>", "pause": "<PERSON><PERSON><PERSON>", "pauseMsg": "Bạn có muốn tạm dừng voucher này không?", "resume": "<PERSON><PERSON><PERSON><PERSON>", "resumeMsg": "Bạn có muốn tiếp tục voucher này không?", "voucherCalendar": "<PERSON><PERSON><PERSON>", "markComplete": "<PERSON><PERSON><PERSON> d<PERSON>u hoàn thành", "markCompleteMsg": "Bạn có muốn hoàn thành?", "onlyThisVoucher": "Chỉ voucher này", "allFutureVouchers": "Tất cả voucher trong tương lai", "voucherListView": "<PERSON><PERSON>ch", "voucherCalendarView": "<PERSON><PERSON><PERSON>", "oneTime": "<PERSON><PERSON><PERSON>", "automated": "<PERSON><PERSON> động", "repeatMsg": "{title}", "@repeatMsg": {"description": "title", "placeholders": {"title": {"type": "String"}}}, "weekly": "<PERSON><PERSON><PERSON> t<PERSON>", "daily": "<PERSON><PERSON><PERSON>", "notEnoughCredits": "Không đủ tín dụng", "voucherDelivered": "Đ<PERSON> gửi", "voucherEmail": "Email", "smsVoucher": "Sms", "pushVoucher": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON> đ<PERSON>y", "failed": "<PERSON><PERSON><PERSON><PERSON> thành công", "opened": "Đã mở", "emailsClicked": "<PERSON><PERSON> nh<PERSON>n vào", "creditsUsed": "<PERSON> đã sử dụng", "voucherDate": "<PERSON><PERSON><PERSON>", "every": "Mỗi", "repeatOn": "Lặp lại trên", "endsOn": "<PERSON><PERSON><PERSON> th<PERSON> v<PERSON>o", "alert": "<PERSON><PERSON><PERSON><PERSON> báo", "claimWindow": "<PERSON><PERSON><PERSON><PERSON> gian <PERSON>h<PERSON>n", "repeatAfterCompletion": "Lặp lại", "day": "<PERSON><PERSON><PERSON>", "year": "Năm", "atTimeOfEvent": "<PERSON><PERSON><PERSON> thời điểm diễn ra sự kiện", "fiveMinutesBefore": "Trước 5 phút", "fifteenMinutesBefore": "Trước 15 phút", "thirtyMinutesBefore": "Trước 30 phút", "oneHourBefore": "Trước 1 giờ", "twoHourBefore": "Trước 2 giờ", "on": "vào", "and": "và", "monday": "<PERSON><PERSON><PERSON> hai", "tuesday": "<PERSON><PERSON><PERSON> ba", "wednesday": "<PERSON><PERSON><PERSON> tư", "thursday": "<PERSON><PERSON><PERSON> năm", "friday": "<PERSON><PERSON><PERSON> s<PERSON>u", "saturday": "<PERSON><PERSON><PERSON> b<PERSON><PERSON>", "sunday": "<PERSON>ủ <PERSON>h<PERSON>", "viewBy": "<PERSON>em theo", "markCompleteOneMsg": "Bạn có muốn hoàn tất voucher này không?", "anytime": "<PERSON><PERSON><PERSON> cứ lúc nào", "endDateRequired": "<PERSON><PERSON><PERSON> kết thúc phải lớn hơn giờ bắt đầu", "applyChangesTo": "<PERSON><PERSON><PERSON><PERSON> hiện thay đổi đối với: ", "thisVoucher": "Voucher hiệ<PERSON> tại", "thisVoucherSub": "Chỉ áp dụng cho voucher này", "allVoucher": "Toàn bộ vouchers", "allVoucherSub": "<PERSON><PERSON> dụng cho voucher hiện tại và các voucher trong tương lai", "claimTimeOptions": "<PERSON><PERSON><PERSON><PERSON> gian <PERSON>h<PERSON>n", "voucherDateTitle": "<PERSON><PERSON><PERSON> voucher", "repeatAfterCompletionTitle": "Lặp lại", "repeatEndDay": "<PERSON><PERSON><PERSON> th<PERSON> v<PERSON>o", "claimTimeOptionsSubTitle": "Chọn thời gian để nhận voucher. Mặc định là bất kỳ lúc nào, cho phép nhận voucher vào bất cứ thời điểm nào. Chọn các tùy chọn khác để đặt thời gian cụ thể.", "maximumQuantityIsRequired": " S<PERSON> lượng tối đa là bắt buộc", "redeemDateRequired": "<PERSON><PERSON><PERSON> bắt đầu đổi voucher phải bằng hoặc lớn hơn giờ bắt đầu nhận voucher", "never": "K<PERSON>ô<PERSON>", "helpMeChoose": "<PERSON><PERSON><PERSON><PERSON> tô<PERSON>n", "aiPoweredFeatureThatHelpsCustomers": "<PERSON><PERSON><PERSON> năng hỗ trợ AI gi<PERSON>p khách hàng chọn sản phẩm họ mong muốn thông qua trò chuyện hoặc đề xuất.", "enableDisableHelpMeChoose": "Bật/Tắt hỗ trợ AI", "enableHelpMeChoose": "<PERSON><PERSON><PERSON>", "disabledHelpMeChoose": "Tắt", "dataHelpMeChoose": "<PERSON><PERSON> liệu", "importData": "<PERSON><PERSON><PERSON><PERSON> liệu", "configureTheInputData": "<PERSON>iều chỉnh dữ liệu để AI hiểu rõ hơn về sản phẩm, dịch vụ của bạn và đưa ra những gợi ý phù hợp nhất.", "helpMeChooseDataEmpty": "Hiện tại không có dữ liệu nào khả dụng, bạn có muốn tạo dữ liệu ngay bây giờ không?", "importAImageFile": "<PERSON><PERSON><PERSON><PERSON> dữ liệu từ hình <PERSON>nh", "importATextFile": "<PERSON><PERSON><PERSON> lên tệp dữ li<PERSON> (.csv, .txt)", "manualTextInput": "<PERSON><PERSON><PERSON><PERSON> dữ liệu thủ công", "dataPreview": "<PERSON><PERSON>", "fileUploadFailed": "<PERSON><PERSON><PERSON><PERSON> dữ liệu không thành công.", "uploading": "<PERSON><PERSON> t<PERSON> lên", "wouldYouLikeToReplaceAllTheCurrentContent": "Bạn muốn chèn thêm dữ liệu mới vào dữ liệu hiện có hay thay thế toàn bộ bằng dữ liệu mới?", "hintTextImportDataHelpMeChoose": "<PERSON><PERSON><PERSON><PERSON> bất kỳ thông tin nào bạn muốn, g<PERSON><PERSON> ý theo tên, sở thích hoặc mô tả, .v.v.", "leave": "<PERSON><PERSON><PERSON> đi", "dataProcessing": "<PERSON>ử lý dữ liệu", "insertData": "<PERSON><PERSON><PERSON> d<PERSON> liệu", "replaceData": "<PERSON>hay thế dữ liệu", "accessToPhotosHasNotBeenGrated": "<PERSON><PERSON><PERSON><PERSON> t<PERSON><PERSON> cập <PERSON>nh ch<PERSON>a đ<PERSON><PERSON><PERSON> cấp", "youNeedToGrantPermissionToAccessThePhotoLibrary": "<PERSON><PERSON><PERSON> cần cấp quyền để có thể truy cập vào thư viện <PERSON>nh.", "accessToCameraHasNotBeenGrated": "<PERSON><PERSON><PERSON><PERSON> truy cập máy <PERSON>nh chưa đ<PERSON><PERSON><PERSON> cấp", "youNeedToGrantPermissionToAccessTheCamera": "<PERSON><PERSON>n cần cấp quyền để có thể truy cập vào máy ảnh của thiết bị.", "goToSettings": "<PERSON>i tới <PERSON>i đặt", "processingDataPleaseWait": "<PERSON><PERSON> xử lý dữ liệu, vui lòng đợi gi<PERSON> lát ...", "allowUserToGetHelp": "Cho phép người dùng gọi sự trợ giúp?", "requestName": "<PERSON><PERSON><PERSON> yêu c<PERSON>u", "requestIcon": "Chọn icon", "widgetAppearance": "<PERSON><PERSON><PERSON> tiện <PERSON>ch", "reorderServices": "<PERSON><PERSON><PERSON> x<PERSON><PERSON> lại d<PERSON>ch vụ", "reorderTables": "<PERSON><PERSON><PERSON> x<PERSON><PERSON> lại bàn", "multipleHomeEngagementSubtitle": "<PERSON><PERSON><PERSON> thống kê gi<PERSON>p tóm tắt thông tin về hiệu suất của cửa hàng.", "multipleHomeCustomerSubtitle": "<PERSON>ra c<PERSON><PERSON> n<PERSON>h thông tin kh<PERSON>ch hàng, tạo tài kho<PERSON>n mới hoặc tặng thưởng.", "multipleHomeStickyQRSubtitle": "Tặng điể<PERSON>, gắn thẻ phân biệt khách hàng mục tiêu cụ thể, hỗ trợ in nhãn QR.", "multipleHomePointClaimsQRSubtitle": "<PERSON> phép khách hàng tạo yêu cầu về điểm bằng cách quét mã QR nhận điểm từ cửa hàng.", "multipleHomeCallButtonQRSubtitle": "<PERSON><PERSON><PERSON> hợ<PERSON> Call Button để hỗ trợ khách hàng một cách nhanh chóng và hiệu quả.", "multipleHomeVouchersCalendarSubtitle": "<PERSON><PERSON><PERSON> thị thông tin chi tiết về các voucher theo từ<PERSON>, g<PERSON><PERSON><PERSON> vi<PERSON><PERSON> quản lý trở nên dễ dàng hơn.", "multipleHomeFeatureEmpty": "<PERSON><PERSON><PERSON><PERSON> có dữ liệu để hiển thị ngay bây giờ!", "enterPageName": "<PERSON><PERSON><PERSON><PERSON> tên trang chủ", "getHelp": "<PERSON>hậ<PERSON> trợ gi<PERSON>p", "cart": "Giỏ hàng", "enableShoppingCart": "Mở tính năng mua sắm", "allowAiToSendSupportRequest": "Cho phép AI gửi yêu cầu hỗ trợ khi khách hàng cần trợ giúp.", "enableTheShoppingCartForAI": "<PERSON><PERSON><PERSON> t<PERSON>h năng giỏ hàng tự động để AI hỗ trợ khách hàng, thêm sản phẩm vào giỏ và báo cho doanh nghiệp.", "background": "<PERSON><PERSON><PERSON>", "addFeatures": "<PERSON><PERSON><PERSON><PERSON> t<PERSON> n<PERSON>ng", "pointsRedeemed": "<PERSON><PERSON><PERSON><PERSON> đã đổi", "voucherRedeemed": "Vouchers đã đổi", "voucherClaimed": "Vouchers đã nhận", "cbFloor": "Tầng 1", "cbFloorTable": "<PERSON><PERSON>n {table}", "@cbFloorTable": {"description": "table", "placeholders": {"table": {"type": "String"}}}, "setDefaultHome": "Đặt mặc định", "homeNameRequired": "<PERSON><PERSON><PERSON> trang chủ là bắt buộc", "addWidget": "<PERSON><PERSON><PERSON><PERSON>", "msgShowInAppOnly": "Chỉ dành cho ứng dụng", "startAt": "<PERSON><PERSON><PERSON> đ<PERSON>u", "widgetsEmpty": "<PERSON><PERSON><PERSON><PERSON> có tiện ích nào có sẵn để hoạt động", "integrations": "<PERSON><PERSON><PERSON>", "featurePreview": "Feature Preview", "shareCopied": "Đã sao chép", "manageTablesServices": "Q<PERSON><PERSON><PERSON> lý b<PERSON>n & <PERSON><PERSON><PERSON> vụ", "additionalFeatures": "<PERSON><PERSON><PERSON> n<PERSON> b<PERSON> sung", "helpMeChooseTitle": "<PERSON><PERSON>ú<PERSON>", "more": "<PERSON><PERSON><PERSON><PERSON> h<PERSON>n", "goToMyAccount": "<PERSON><PERSON>n tài kho<PERSON>n của tôi", "additionalButtons": "<PERSON><PERSON><PERSON> b<PERSON> sung", "thankYouVoucherCalendar": "<PERSON>h<PERSON><PERSON> phá lịch trình ưu đãi của chúng tôi để nhận được ưu đãi độc quyền hôm nay!", "thankYouAnnouncementContent": "<PERSON><PERSON> tả nội dung thông báo cửa hàng sẽ hiển thị tại đây", "customerCheckin": "<PERSON><PERSON><PERSON><PERSON>-In", "allowCustomerCheckInEarnPointAndMore": "<PERSON> phép khách hàng đăng ký, tích điểm và hơn thế nữa.", "allowCustomerCheckIn": "<PERSON> phép kh<PERSON>ch hàng Check-In", "enterLabelName": "<PERSON><PERSON><PERSON><PERSON> tên nh<PERSON>n", "buttonName": "<PERSON><PERSON><PERSON>", "enabledCheckin": "<PERSON><PERSON><PERSON>", "disabledCheckin": "Tắt", "earnAdditionalPoints": "<PERSON>ếm thêm điểm?", "earnAdditionalPointsSubTitle": "<PERSON><PERSON><PERSON><PERSON> hàng sẽ nhận được điểm khi check-in và nút \"Tặng điểm\" sẽ xuất hiện trong ứng dụng quản lý của bàn đó.", "checkedIn": "Checked In", "searchingForInformationPleaseWait": "<PERSON><PERSON> tìm kiếm thông tin kh<PERSON>ch hàng, vui lòng đợi ...", "noUsersHaveCheckedIn": "<PERSON><PERSON><PERSON> có khách hàng nào check-in.", "balance": "<PERSON><PERSON><PERSON><PERSON>", "instrutionHeaderList": "Tu<PERSON> chỉnh màn hình linh ho<PERSON>t", "instrutionHeaderListSub": "Nhấn vào đây để quản lý và tùy chỉnh màn hình linh hoạt.", "instrutionHeaderAdd": "<PERSON><PERSON><PERSON><PERSON> màn hình mới", "instrutionHeaderAddSub": "Nhấn vào đây để tạo các tiện tích tùy chỉnh theo sở thích của bạn.", "instrutionHeaderAddHomeName": "<PERSON><PERSON><PERSON> trang chủ", "instrutionHeaderAddHomeNameSub": "<PERSON>ên trang chủ mong muốn để dễ dàng quản lý.", "instrutionHeaderAddWidget": "<PERSON><PERSON><PERSON><PERSON> tiện tích", "instrutionHeaderAddWidgetSub": "Nhấn vào đây để mở bảng tiện tích với tất cả các tính năng bạn cần.", "instrutionHeaderAddWidgetFirsr": "<PERSON><PERSON><PERSON><PERSON> các t<PERSON>h năng tiện ích", "instrutionHeaderAddWidgetFirsrSub": "Nhấn vào đây để thêm các tiện tích hữu ích mà bạn muốn hiển thị trên trang chủ.", "instrutionHeaderAddColor": "<PERSON><PERSON><PERSON> m<PERSON>u", "instrutionHeaderAddColorSub": "Nhấn vào đây để mở bảng màu cho nền trang chủ.", "instrutionHeaderAddColorBackground": "<PERSON><PERSON><PERSON>", "instrutionHeaderAddColorBackgroundSub": "<PERSON>hấn để chọn màu yêu thích của bạn.", "instrutionHeaderArrange": "Xóa", "instrutionHeaderArrangeSub": "<PERSON><PERSON><PERSON> tiện ích tại đây.", "instrutionHeaderWidgetArrange": "<PERSON><PERSON><PERSON>p", "instrutionHeaderWidgetArrangeSub": "<PERSON>ạn có thể chạm và giữ một tiện tích để di chuyển nó khi có nhiều tiện tích.", "instrutionHeaderWidgetDoneSub": "<PERSON><PERSON><PERSON><PERSON>, nhấn vào đây để hoàn tất.", "instrutionButtomSheetHeader1": "Hey, Giờ Bạn Có Thể", "instrutionButtomSheetHeader2": "<PERSON><PERSON><PERSON>n <PERSON>ù<PERSON> Chỉnh", "instrutionButtomSheetHeader3": "<PERSON><PERSON><PERSON>", "instrutionButtomSheetContent1": "<PERSON><PERSON> Nhân Hóa M<PERSON>", "instrutionButtomSheetContent1Sub": "T<PERSON><PERSON> chỉnh tiện ích, bố cục và màu sắc để phù hợp với phong cách độc đáo của cửa hàng bạn, tạo ra một giao diện tiện lợi.", "instrutionButtomSheetContent2": "<PERSON><PERSON><PERSON>", "instrutionButtomSheetContent2Sub": "<PERSON><PERSON><PERSON><PERSON> lập và chuyển đổi giữa các màn hình cho nhiều mục đích khác nhau.", "instrutionButtomSheetContent3": "<PERSON><PERSON><PERSON><PERSON>", "instrutionButtomSheetContent3Sub": "<PERSON><PERSON><PERSON><PERSON> các tiện ích cho l<PERSON><PERSON><PERSON> tư<PERSON><PERSON> t<PERSON>, đ<PERSON><PERSON> đ<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, thông tin khách hàng và nhiều tính năng khác.", "instrutionEnableSub": "Nhấn để hiển thị hoặc ẩn màn hình.", "setAsDefaultMsg": "Bạn có muốn đặt màn hình hiện tại thành mặc định không?", "homeEmptyOwner": "<PERSON><PERSON>a có tiện tích nào. Nhấn vào biểu tượng tùy chỉnh để thêm", "homeEmptyStaff": "<PERSON><PERSON><PERSON> tại không có tiện tích nào.", "letUsKnowYoureHere": "<PERSON><PERSON><PERSON> cho chúng tôi biết bạn đang ở đây!", "checkIn": "Check-In", "helpAndSupport": "Tr<PERSON> gi<PERSON><PERSON> & <PERSON><PERSON><PERSON> cầu hỗ trợ", "getStickyQRSupport": "Nhận hỗ trợ từ StickyQR", "termsAndPrivacy": "Đ<PERSON><PERSON><PERSON> & <PERSON><PERSON><PERSON><PERSON> riêng tư", "helpWith": "<PERSON>rợ giúp với", "callButtonOptions": "Cài đặt Call Button", "tooltipDisable": "<PERSON><PERSON><PERSON> hình đã được đặt mặc định, chức năng này không khả dụng.", "pressAndHold": "Nhấn và giữ để di chuyển vị trí của tiện ích.", "aiSummary": "AI Tóm Tắt", "unableToFindCustomerInfomation": "<PERSON>hông tìm thấy thông tin khách hàng.", "sendCustomerAnnouncements": "<PERSON><PERSON><PERSON> thông báo cho khách hàng", "sendCustomerAnnouncementsSub": "Thông báo cho khách hàng qua ứng dụng hoặc email bằng cách chọn các tùy chọn bên dướ<PERSON>.", "appPush": "App PUSH (ứng dụng StickyQR)", "emailPush": "E-mail", "subject": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON>", "emailSubjectIsRequired": "Ti<PERSON><PERSON> đề là b<PERSON><PERSON> buộc", "annOffWarning": "Thông báo đã tắt. <PERSON><PERSON><PERSON><PERSON> hàng sẽ không nhận đư<PERSON><PERSON> thông báo.", "enableNow": "<PERSON><PERSON><PERSON>y Giờ", "createAIAnnouncement": "<PERSON><PERSON><PERSON> thông báo AI", "createAIAnnouncementDialogMsg": "Bạn có muốn sử dụng AI để tạo thông báo cho khách hàng về voucher này không?", "pro": "Pro", "moreMenuTitle": "Nhiều hơn", "hide": "Ẩn các t<PERSON>h n<PERSON>ng", "featuresMenu": "c<PERSON><PERSON> t<PERSON> n<PERSON>ng", "featureMsg": "<PERSON><PERSON><PERSON>", "standard": "Standard", "enterprise": "Enterprise", "upgrade": "<PERSON><PERSON><PERSON> c<PERSON>p", "showMore": "Mở rộng", "showLess": "<PERSON><PERSON>", "buyCreditBilling": "<PERSON><PERSON> xu", "planStandard": "<PERSON><PERSON><PERSON><PERSON>", "planPro": "Pro", "planEnterprise": "<PERSON><PERSON><PERSON>", "setupPrinter": "<PERSON><PERSON><PERSON><PERSON> l<PERSON> in", "retryingPrint": "<PERSON><PERSON> thử in lại...", "retryingPrintAgain": "Vẫn không in được, vui lòng kiểm tra lại máy in", "printingFailed": "<PERSON><PERSON><PERSON><PERSON> in được, thử lại sau {durationTime}s...", "@printingFailed": {"description": "durationTime", "placeholders": {"durationTime": {"type": "String"}}}, "printingLastFailed": "Vẫn không in được, thử lần cuối sau {durationTime}s...", "@printingLastFailed": {"description": "durationTime", "placeholders": {"durationTime": {"type": "String"}}}, "setUpPrinter": "<PERSON><PERSON><PERSON><PERSON> l<PERSON>", "newOrders": "<PERSON><PERSON><PERSON>", "newOrder": "<PERSON><PERSON><PERSON>", "newScheduleOrder": "Mới, Đặt trước", "inProgressOrder": "<PERSON><PERSON> làm", "readyOrder": "Sẵn sàng", "scheduledOrder": "Đặt trước", "lengthTotalItems": "{count} TỔNG SẢN PHẨM", "@lengthTotalItems": {"description": "count", "placeholders": {"count": {"type": "String"}}}, "unavailable": "<PERSON><PERSON><PERSON>", "unavailableToday": "<PERSON><PERSON><PERSON> hết hôm nay", "unavailableIn3Hours": "<PERSON><PERSON><PERSON> hết trong 3h", "categories": "<PERSON><PERSON>", "allItems": "<PERSON><PERSON><PERSON> c<PERSON>", "searchForAnItem": "<PERSON><PERSON><PERSON>", "orderStatus": "Tr<PERSON>ng thái: {status}", "@orderStatus": {"description": "status", "placeholders": {"status": {"type": "String"}}}, "subtotal": "<PERSON><PERSON><PERSON>", "tax": "<PERSON><PERSON><PERSON>", "markInProgress": "<PERSON><PERSON> lý", "markReady": "Đã sẵn sàng", "markCompleted": "<PERSON><PERSON> hoàn thành", "issueWithOrderActive": "Sự cố đơn hàng", "noOrdersyet": "<PERSON><PERSON><PERSON> có đơn hàng nào", "tapToViewAndConfirm": "<PERSON>hấn để xem và xác nhận", "orderScheduledStatus": "<PERSON><PERSON><PERSON>", "orderNewStatus": "Đ<PERSON><PERSON> hàng mới", "@orderNewStatus": {"description": "count", "placeholders": {"s": {"type": "String"}}}, "specialRequirements": "<PERSON><PERSON><PERSON> cầu đặc biệt", "pickupAt": "<PERSON><PERSON><PERSON><PERSON> {time}", "@pickupAt": {"description": "time", "placeholders": {"time": {"type": "String"}}}, "itemsOrder": "{count} s<PERSON><PERSON> ph<PERSON>m", "@itemsOrder": {"description": "s", "placeholders": {"count": {"type": "int"}}}, "remindMe": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>i", "managementProducts": "<PERSON><PERSON><PERSON><PERSON> lý sản phẩm", "exitOrdering": "<PERSON><PERSON><PERSON><PERSON> khỏi trang đặt hàng", "readyIn": "Sẵn sàng", "now": "Bây giờ", "expired": "Đ<PERSON> quá hạn", "items": "<PERSON><PERSON><PERSON> p<PERSON>m", "modifiers": "<PERSON><PERSON><PERSON><PERSON> tù<PERSON> ch<PERSON>n", "searchModifiers": "<PERSON><PERSON><PERSON> kiếm nhóm tùy chọn", "searchCategories": "<PERSON><PERSON><PERSON> ki<PERSON>m danh mục", "noCategoriesYet": "<PERSON><PERSON><PERSON> có danh mục nào.", "noItemsYet": "<PERSON><PERSON><PERSON> c<PERSON> sản phẩm nào.", "noModifiersYet": "<PERSON><PERSON><PERSON> có nhóm tùy chọn nào.", "allModifiers": "<PERSON><PERSON><PERSON> c<PERSON>", "ordersHistory": "<PERSON><PERSON><PERSON> sử đơn hàng", "odHistoryOrderPickedUp": "<PERSON><PERSON><PERSON> hàng đã nhận", "odHistoryOrderDetail": "<PERSON> tiết đơn hàng", "selectedAnOrderToViewDetail": "<PERSON><PERSON><PERSON> đơn hàng để xem chi tiết", "odHistoryIssueRefund": "<PERSON><PERSON><PERSON> ti<PERSON>n", "odHistoryConfirmRefund": "<PERSON><PERSON><PERSON>n hoàn tiền", "odHistoryTotalItems": "Tổng số mặt hàng", "odHistoryTotalItem": "Tổng số mặt hàng", "odHistorySubtotal": "<PERSON><PERSON><PERSON>", "odHistoryTax": "<PERSON><PERSON><PERSON>", "odHistoryTotal": "<PERSON><PERSON><PERSON> cộng", "odIssueRefundItem": "Mặt hàng", "odIssueRefundAmountTitle": "<PERSON><PERSON> tiền", "odIssueRefundAmount": "<PERSON><PERSON> tiền hoàn lại", "odIssueRefundItems": "<PERSON><PERSON><PERSON> tiền {itemsCount} mặt hàng", "@odIssueRefundItems": {"description": "itemsCount", "placeholders": {"itemsCount": {"type": "String"}}}, "odIssueRefundRefundTo": "<PERSON><PERSON><PERSON> tiền cho", "odIssueRefundReasonForRefund": "Lý do hoàn tiền?", "odIssueRefundMaximumRefundAmountIs": "<PERSON><PERSON> tiền hoàn lại tối đa là", "odIssueRefundAmountToRefund": "<PERSON><PERSON> tiền hoàn lại", "odIssueRefundEnterAmountToRefund": "<PERSON><PERSON><PERSON><PERSON> số tiền muốn hoàn lại", "odIssueRefundMaximumIs": "S<PERSON> tiền tối đa là", "settingsOrders": "Cài đặt Eat.chat AI Ordering", "autoConfirmNewOrder": "Tự động xác nhận đơn hàng mới", "storeHours": "Giờ mở cửa", "orderAlert": "<PERSON><PERSON><PERSON> b<PERSON><PERSON> đơn hàng", "orderReceiptPrinters": "M<PERSON>y in hóa đơn", "specialHours": "Giờ mở cửa", "regularOpeningHours": "Giờ mở cửa thông thường", "theseAreHoursYourStoreIsAvailable": "<PERSON><PERSON><PERSON> là những giờ cửa hàng của bạn mở cửa", "specialHoursAndClosures": "Giờ mở/đóng cửa các ngày đặc biệt", "addSpecialHours": "<PERSON>hê<PERSON> giờ mở cửa đặc biệt", "orderSoundSettings": "Cài đặt âm thanh", "orderNotificationSound": "<PERSON><PERSON> thanh thông báo", "silent": "Im lặng", "areYouSureYouWantToRefund": "Bạn có chắc chắn muốn hoàn lại tiền?", "refundEntireOrderRemainingBalance": "<PERSON><PERSON>n lại toàn bộ đơn hàng / số dư còn lại", "youDontHaveAnyOrderHistoryYet": "Bạn chưa có lịch sử đơn hàng nào!", "issueWithOrder": "Sự cố đơn hàng", "outOfStock": "<PERSON><PERSON><PERSON>", "adjustPrepTime": "<PERSON>iều chỉnh thời gian chuẩn bị", "cancelOrder": "<PERSON><PERSON><PERSON> đơn hàng", "whyDoYouWantToCancelTheOrder": "Lý do bạn muốn hủy đơn hàng?", "storeIsTooBusy": "<PERSON><PERSON><PERSON> hàng đang bận", "theStoreIsClosing": "<PERSON><PERSON><PERSON> hàng đang đóng cửa", "productIsOutOfStock": "<PERSON><PERSON><PERSON> phẩm đã hết hàng", "needMorePrepTime": "<PERSON><PERSON><PERSON> thêm thời gian chu<PERSON>n bị", "needMorePrepTimeMsg": "Cần thêm thời gian để chuẩn bị đơn hàng này? Bạn có thể điều chỉnh thời gian nhận hàng từ chế độ xem chi tiết đơn hàng bằng cách nhấn vào các nút -5 hoặc +5.", "gotIt": "<PERSON><PERSON> hiểu", "pleaseStateReason": "<PERSON><PERSON> lòng cho biết lý do của bạn", "noKeepThisOrder": "<PERSON><PERSON><PERSON><PERSON> tục với đơn hàng", "yesCancelThisOrder": "Huỷ đơn hàng", "tellCustomersWhy": "<PERSON><PERSON><PERSON> cho khách hàng biết lý do của bạn", "noItemSelected": "<PERSON><PERSON><PERSON> có mục nào đ<PERSON><PERSON><PERSON> chọn", "selectedItems": "<PERSON><PERSON><PERSON> mục đ<PERSON> ch<PERSON>n", "chooseItemOutOfStock": "<PERSON><PERSON><PERSON> sản phẩm đã hết hàng", "howLongWillItTakeToBeBackInStock": "<PERSON><PERSON>t bao lâu để có hàng trở lại?", "howWouldYouLikeToHandleTheOrderIssue": "Bạn muốn xử lý vấn đề đơn hàng như thế nào?", "refundForThisItem": "<PERSON><PERSON><PERSON> tiền cho sản phẩm này", "replaceTheItem": "<PERSON><PERSON> thế sản phẩm", "refundConfirmation": "<PERSON><PERSON><PERSON>n hoàn tiền", "commingSoon": "S<PERSON><PERSON> ra mắt", "in4Hours": "Trong 4 giờ", "endOfToday": "<PERSON><PERSON><PERSON> ng<PERSON>y hôm nay", "indefinitely": "<PERSON><PERSON> thời hạn", "ultil": "<PERSON><PERSON><PERSON>", "untilManuallyReactivated": "<PERSON><PERSON><PERSON> khi đ<PERSON><PERSON><PERSON> k<PERSON>ch ho<PERSON> lại", "productsManage": "<PERSON><PERSON><PERSON> p<PERSON>m", "activeOrders": "Đơn đang xử lý", "stickyOrders": "EAT.CHAT AI ORDERING", "confirmation": "<PERSON><PERSON><PERSON>", "noRemindMe": "K<PERSON>ô<PERSON>", "confirmScheduleProgress": "Bạn đang thực hiện một đơn hàng sớm hơn **{time}** so với thời gian dự kiến. Bạn chắc chắn muốn thực hiện đơn hàng này ngay bây giờ", "@confirmScheduleProgress": {"description": "time", "placeholders": {"time": {"type": "String"}}}, "days": "ng<PERSON>y", "@days": {"description": "s", "placeholders": {"s": {"type": "String"}}}, "hours": "giờ", "@hours": {"description": "s", "placeholders": {"s": {"type": "String"}}}, "minutes": "<PERSON><PERSON><PERSON><PERSON>", "@minutes": {"description": "s", "placeholders": {"s": {"type": "String"}}}, "acceptOnlineOrdering": "Nhận đặt hàng online", "yesterday": "<PERSON><PERSON><PERSON> qua", "refunded": "<PERSON><PERSON><PERSON> l<PERSON>", "updateStoreStatus": "<PERSON><PERSON><PERSON> nhật trạng thái cửa hàng", "normal": "<PERSON><PERSON><PERSON>", "busy": "<PERSON><PERSON> b<PERSON>n", "acceptingOrders": "<PERSON><PERSON>ận đơn đặt hàng", "morePrepTime": "<PERSON><PERSON><PERSON><PERSON> th<PERSON>i gian ch<PERSON> bị", "noNewOrders": "<PERSON><PERSON><PERSON><PERSON> nhận đơn hàng mới", "yourStoreIsAvailableAndAcceptingOrders": "Cửa hàng của bạn đang mở cửa và cho phép đặt hàng", "customersCanPlaceOrdersWithinStandardTimesOnTheStoreOrderingChannels": "<PERSON><PERSON><PERSON><PERSON> hàng có thể đặt hàng trong thời gian quy định trên các kênh đặt hàng của cửa hàng.", "changeStatus": "Thay đổi trạng thái", "howMuchAdditionalPrepTimeDoYouNeed": "<PERSON>ạn cần thêm bao nhiêu thời gian chuẩn bị?", "prepTime": "<PERSON><PERSON><PERSON> thêm", "howlongWouldYouLikeToPauseNewOrders": "Bạn muốn tạm dừng nhận đơn hàng mới trong bao lâu?", "pauseFor": "<PERSON><PERSON><PERSON> dừng trong", "pauseForEndOfToday": "<PERSON><PERSON><PERSON> dừng đến", "customersWillNoLongerBeAbleToPlaceOrdersOnTheStoreOrderingChannels": "<PERSON><PERSON><PERSON><PERSON> hàng sẽ không thể đặt mua sản phẩm trên các kênh của cửa hàng.", "weAddMinutesToYourEstimatedOrder": "Thời gian hoàn thành đơn hàng dự kiến của bạn sẽ được cập nhật thêm {time} phút trên cửa hàng trực tuyến và ứng dụng chat đặt hàng", "@weAddMinutesToYourEstimatedOrder": {"description": "time", "placeholders": {"time": {"type": "String"}}}, "itWillReturnToNormalPrepTimeAfterMin": "<PERSON>h<PERSON>i gian chuẩn bị sẽ trở lại bình thường sau {time}.", "@itWillReturnToNormalPrepTimeAfterMin": {"description": "time", "placeholders": {"time": {"type": "String"}}}, "min": "<PERSON><PERSON><PERSON><PERSON>", "mins": "<PERSON><PERSON><PERSON><PERSON>", "updateStoreHours": "giờ", "acceptingOrdersUntil": "<PERSON><PERSON><PERSON><PERSON> đơn hàng đến", "pauseAcceptingOrdersUntil": "<PERSON><PERSON><PERSON> nhận đơn đến", "updateStoreEndOfToday": "h<PERSON>t ng<PERSON>y hôm nay", "storesWillPauseAndStopReceivingOrders": "<PERSON><PERSON><PERSON> hàng sẽ tạm dừng và không nhận đơn hàng", "customersWillTemporarilyBeUnableToPlaceOrdersOnTheOnlineStoreAndOrderingChatApp": "<PERSON><PERSON><PERSON><PERSON> hàng sẽ tạm thời không thể đặt hàng trên cửa hàng trực tuyến và ứng dụng chat đặt hàng.", "theStoreWillReturnToNormalOperationsAfter": "<PERSON><PERSON><PERSON> hàng sẽ trở lại hoạt động bình thường sau {time}.", "@theStoreWillReturnToNormalOperationsAfter": {"description": "time", "placeholders": {"time": {"type": "String"}}}, "theStoreIsTemporarilyPausingOrderTakingUntilTheEndOfToday": "<PERSON><PERSON><PERSON> hàng sẽ tạm thời dừng nhận đơn hàng cho đến hết ngày hôm nay.", "allowOnlineOrdering": "<PERSON> phép đặt hàng trực tuyến", "whenTurnedOffTheStoreWillRemainClosedUntilTurnedBackOn": "<PERSON><PERSON><PERSON> tắt t<PERSON>h năng <PERSON>à<PERSON>, cửa hàng sẽ ở trạng thái đóng cửa cho đến khi được bật trở lại.", "closed": "<PERSON><PERSON><PERSON>", "needsAction": "<PERSON><PERSON><PERSON> lý", "undo": "<PERSON><PERSON><PERSON>", "undoMsg": "Bạn đã chuyển trạng thái từ **{status1}** sang **{status2}**. Bạn có muốn hoàn tác không?", "@undoMsg": {"description": "msg", "placeholders": {"status1": {"type": "String"}, "status2": {"type": "String"}}}, "unavailableTodayCategory": "<PERSON><PERSON> mục tạm hết hôm nay", "unavailableCategory": "<PERSON><PERSON> m<PERSON><PERSON> tạm hết", "availableCategory": "<PERSON><PERSON> mục có sẵn", "availableCategoryMsg": "Bạn có thể chỉnh sửa các mục của mình nhưng chúng sẽ bị ẩn cho đến khi bạn đưa danh mục có sẵn.", "orders": "<PERSON><PERSON><PERSON> hàng", "ordersWidgetMsg": "Tính năng Active Orders gi<PERSON>p nhân viên theo dõi và quản lý đơn hàng theo thời gian thực, dễ dàng cập nhật trạng thái và xử lý nhanh chóng.", "returned": "<PERSON><PERSON><PERSON> h<PERSON>", "canceled": "Huỷ đơn hàng", "foc": "<PERSON><PERSON><PERSON> phí", "refund": "<PERSON><PERSON><PERSON> ti<PERSON>n", "theAvailableRefundAmountForThisOrderIs": "<PERSON><PERSON> tiền hoàn lại khả dụng cho đơn hàng này là {amount}.", "@theAvailableRefundAmountForThisOrderIs": {"description": "amount", "placeholders": {"amount": {"type": "String"}}}, "thisOrderHasBeenFullyRefunded": "Đ<PERSON><PERSON> hàng này đã được hoàn tiền đầy đủ.", "willYouBePpenOrClosed": "Bạn sẽ mở cửa hay đóng cửa?", "storeOpen": "Giờ mở cửa", "storeClose": "<PERSON><PERSON><PERSON> đ<PERSON>g c<PERSON>a", "selectDates": "<PERSON><PERSON><PERSON>", "open": "Mở cửa", "selectTime": "<PERSON><PERSON><PERSON> thời gian", "hour": "Giờ", "minute": "<PERSON><PERSON><PERSON>", "storeAvailability": "<PERSON><PERSON><PERSON> trạng c<PERSON><PERSON> hàng", "editSpecialHours": "Chỉnh sửa giờ đóng/mở cửa", "openAllDay": "Mở cửa 24/24", "addSpecialHoursOrClosuresForHolidays": "Thêm giờ đặc biệt hoặc giờ đóng cửa cho các ngày lễ, hoặc các sự kiện đặc biệt khác. Điều này sẽ tạm thời thay thế giờ hoạt động thông thường của bạn.", "editRegularOpeningHours": "Sửa giờ mở cửa thông thường", "stickyPointsTitle": "Sticky Points", "integrationStickyPointsSubTitle": "<PERSON><PERSON><PERSON> dựng mối quan hệ lâu dài với khách hàng, khuy<PERSON>n khích họ quay lại bằng cách cung cấp các phần thưởng được cá nhân hóa và quyền truy cập độc quyền vào các ưu đãi và sự kiện.", "stickyVouchersTitle": "Sticky Vouchers", "stickyVouchersSubTitle": "<PERSON><PERSON> d<PERSON> voucher, mã gi<PERSON>m giá để thu hút khách hàng và tối ưu trải nghiệm khách hàng.", "enableStickyPointsTitle": "<PERSON><PERSON><PERSON>", "enableStickyPointsSubTitle": "<PERSON><PERSON>t t<PERSON>h năng 'Sticky Points' để quảng bá nhằm thu hút thêm thành viên tích lũy điểm và đổi thưởng tại cửa hàng.", "pointConversionTitle": "<PERSON><PERSON><PERSON><PERSON>", "pointConversionSubTitle": "<PERSON>ọn số điểm sẽ được quy đổi theo đơn vị tiền tệ của bạn (ví dụ: 1 điểm trên mỗi USD, 1 điểm trên mỗi 10.000 VND, v.v.).", "pointRoundingOffTitle": "<PERSON><PERSON><PERSON>", "pointRoundingOffSubTitle": "Chọn cách làm tròn điểm thưởng cho tổng đơn hàng lẻ, có thể làm tròn về số nguyên gần nhất (7.4 thành 7) hoặc làm tròn lên số nguyên kế tiếp (7.4 thành 8 - mặc định)", "maximumRewardsPerOrderTitle": "<PERSON><PERSON> Phần Thưởng T<PERSON>i Đa Cho Mỗi Đơn Hàng", "maximumRewardsPerOrderSubTitle": "Cho phép tối đa 1 hoặc nhiều phần thưởng có thể áp dụng cho mỗi đơn hàng. Mặc định là tối đa 1 phần thưởng cho mỗi đơn hàng.", "nearestInteger": "<PERSON><PERSON><PERSON> tròn gi<PERSON>m", "nextInteger": "<PERSON><PERSON><PERSON> tròn tăng", "maximumOneReward": "<PERSON><PERSON>i đa một phần thưởng", "multipleRewardsAllowed": "<PERSON> phép nhiều phần thưởng", "comingSoon": "Sắp ra mắt...", "discounts": "G<PERSON>ảm giá", "amountIsRequired": "Số tiền là b<PERSON> buộc", "percentageIsRequired": "<PERSON>ần tr<PERSON>m là bắ<PERSON> bu<PERSON>c", "selectProducts": "<PERSON><PERSON><PERSON> sản ph<PERSON>m", "searchProducts": "<PERSON><PERSON><PERSON> k<PERSON>m sản phẩm", "noProductsFound": "<PERSON><PERSON><PERSON><PERSON> tìm thấy sản phẩm", "selectCategories": "<PERSON><PERSON><PERSON> danh mục", "noCategoriesFound": "<PERSON><PERSON><PERSON><PERSON> tìm thấy danh mục", "rewardTypeHeader": "<PERSON><PERSON><PERSON> hợp Sticky Points với Eat.chat AI ordering (<PERSON><PERSON><PERSON>)", "rewardTypeTitle": "<PERSON><PERSON><PERSON> cách ưu đãi áp dụng cho đơn hàng.", "maxValueRewardTitle": "<PERSON><PERSON><PERSON> trị tối đa (<PERSON><PERSON><PERSON><PERSON> tí<PERSON> lũy vào tổng đơn hàng)", "noMax": "(không giới hạn)", "productCategories": "<PERSON><PERSON> m<PERSON><PERSON> sản ph<PERSON>m", "excludeProducts": "<PERSON><PERSON><PERSON> trừ sản phẩm", "doYouWantToIntegrateStickyPoints": "Bạn muốn tích hợp Sticky Points với Eat.chat AI ordering?", "doYouWantToIntegrateStickyPointsSubtitle": "<PERSON><PERSON><PERSON> hợp để khách hàng có thể tích điểm và lấy ưu đãi mỗi khi họ đặt hàng trực tuyến.", "takeMeThere": "<PERSON><PERSON>, đi đến đó", "productsAreRequired": "<PERSON><PERSON><PERSON> ph<PERSON>m là bắt buộc", "categoriesAreRequired": "<PERSON><PERSON> m<PERSON><PERSON> là b<PERSON> buộc", "chooseRewardType": "<PERSON><PERSON><PERSON> lo<PERSON>i ưu đãi", "percentage": "<PERSON><PERSON><PERSON> tr<PERSON>m", "enterPercentage": "<PERSON><PERSON><PERSON><PERSON> ph<PERSON>n trăm", "myRewardsVouchers": "Ưu đãi & Vouchers", "orderOnline": "Đặt hàng", "referAndEarnMore": "<PERSON><PERSON><PERSON><PERSON> thiệu và nhận thưởng", "selectTags": "<PERSON><PERSON><PERSON> thẻ", "noTagsFound": "<PERSON><PERSON><PERSON><PERSON> tìm thấy thẻ nào", "download": "<PERSON><PERSON><PERSON> dụng", "downloadSubtitle": "<PERSON>hận ưu đãi đặc biệt chỉ có trong ứng dụng", "myAccount": "<PERSON><PERSON><PERSON>n của tôi", "september2025": "<PERSON><PERSON><PERSON><PERSON> 2025", "showMoreTKPage": "<PERSON><PERSON>", "enhanceWithAI": "<PERSON><PERSON>i thi<PERSON>n với AI", "enterYourKeyword": "<PERSON><PERSON><PERSON><PERSON> từ khóa...", "keyword": "<PERSON><PERSON> khóa", "use": "Sử dụng", "announcementsWitchInactive": "Đặt thời gian tự động để thông bá<PERSON> chuyển sang trạng thái không hoạt động.", "dialogOrderOnline": "Bạn chưa thể áp dụng lựa chọn này vì tính năng đặt hàng trực tuyến chưa được kích hoạt. Bạn có muốn kích hoạt tính năng này trong phần cài đặt không?", "skipNotifyingCustomers": "(<PERSON><PERSON><PERSON><PERSON> g<PERSON>i thông báo)", "selectDate": "<PERSON><PERSON><PERSON>", "generateAnn": "Tạo", "autoReadyOrder": "Tự động sẵn sàng cho các đơn đặt hàng đang được tiến hành", "online": "Đặt hàng online", "serviceFee": "<PERSON><PERSON> vụ", "orderTimeline": "Ti<PERSON>n trình đặt hàng", "pickedUp": "<PERSON><PERSON><PERSON><PERSON> hàng", "details": "<PERSON> ti<PERSON>", "readyTime": "<PERSON>h<PERSON>i gian sẵn sàng", "completedTime": "<PERSON><PERSON><PERSON><PERSON> gian hoàn thành", "cancelTime": "<PERSON><PERSON><PERSON><PERSON> gian <PERSON>y", "preparingTime": "<PERSON><PERSON><PERSON><PERSON> gian ch<PERSON>n bị", "scheduledTime": "Th<PERSON>i gian đặt trước", "orderReadyTime": "Đ<PERSON>n hàng sẵn sàng", "orderCompletedTime": "<PERSON><PERSON><PERSON> hàng đã hoàn tất", "orderCancelTime": "Đ<PERSON>n hàng đã huỷ", "orderPreparingTime": "<PERSON><PERSON> chu<PERSON>n bị đơn hàng", "orderScheduledTime": "<PERSON><PERSON><PERSON> hẹn đơn hàng", "yesRegister": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> ký", "registerCustomerHaveAccTitle": "<PERSON>ố điện thoại này đã liên kết với tài khoản người dùng.", "registerCustomerHaveAccMsg": "<PERSON><PERSON> lòng đăng nhập để tạo hồ sơ doanh nghiệp mới.", "enterYourAddress": "<PERSON><PERSON><PERSON><PERSON> địa chỉ", "businessAdressRequired": "Địa chỉ là bắt buộc", "enterYourBusinessName": "<PERSON><PERSON><PERSON><PERSON> tên c<PERSON><PERSON> hàng", "stickyAISupport": "StickyQR AI - Hỗ trợ thông minh", "enterTextstickyAISupport": "<PERSON><PERSON><PERSON> kiếm bất cứ điều gì...", "errorMsgStickyAISupport": "<PERSON><PERSON> xảy ra lỗi. <PERSON><PERSON> vui lòng thử lại...", "rewardRedeemed": "<PERSON><PERSON><PERSON> điểm", "reviewUsOnGoogleTotal": "<PERSON><PERSON><PERSON> gi<PERSON> chúng tôi trên Google", "reviewUsOnGoogleSubtotal": "<PERSON> phép khách hàng chia sẻ trải nghiệm trên Google.", "callButtonOrderItem": "<PERSON><PERSON><PERSON> p<PERSON>m", "callButtonOrderItems": "<PERSON><PERSON><PERSON> p<PERSON>m", "orderTime": "Th<PERSON>i gian đặt", "autoCompleteOrder": "Tự động hoàn thành đơn hàng", "automaticallyClearsAllReadyOrdersAt": "Tự động hoàn thành tất cả đơn hàng sẵn sàng vào lúc:", "automaticallyClearsNote1": "Thời gian tự động hoàn thành bạn chọn nằm trong giờ mở cửa. Hành động này có thể ảnh hưởng đến các đơn hàng đang xử lý.", "automaticallyClearsNote2": "Nên đặt sau ít nhất 1 giờ sau khi đóng cửa.", "orderMore": "<PERSON><PERSON><PERSON>", "orderMoreSubtotal": "<PERSON><PERSON><PERSON> món qua hệ thống đặt hàng.", "useOnlineMenu": "<PERSON><PERSON> dụng thực đơn trực tuyến", "availableCategories": "<PERSON><PERSON> mục có sẵn", "tipNote": "Gợi ý: ", "orderMoreTitle": "<PERSON><PERSON><PERSON> món", "orderMoreSubtitle": "<PERSON><PERSON>t t<PERSON>h năng này  để khách hàng có thể gọi món một cách nhanh chóng và hiệu quả.", "categoriesNoProductsFound": "<PERSON><PERSON><PERSON><PERSON> tìm thấy sản phẩm trong danh mục đã chọn.", "payAtStore": "<PERSON><PERSON> to<PERSON> tại cửa hàng", "unPaid": "<PERSON><PERSON><PERSON> to<PERSON>", "managePayment": "<PERSON><PERSON><PERSON><PERSON> lý <PERSON>h toán", "payByCard": "Thanh toán bằng thẻ", "payByCash": "<PERSON><PERSON> toán bằng tiền mặt", "payByApplePay": "Apple Pay", "payByGooglePay": "Google Pay", "collectCash": "<PERSON><PERSON><PERSON> nhận đã thanh toán tiền mặt cho đơn này?", "cardPayments": "Thanh toán thẻ", "cash": "Tiền mặt", "markAsPaidInCash": "<PERSON><PERSON> toán bằng tiền mặt", "sendPaymentLinkTitle": "<PERSON><PERSON><PERSON> liên kết thanh toán", "editContent": "Chỉnh sửa nội dung", "startOver": "<PERSON><PERSON><PERSON> lại từ đầu", "startOverSub": "(<PERSON><PERSON><PERSON><PERSON> phục cài đặt gốc)", "autoConfirm": "<PERSON><PERSON> động x<PERSON>c n<PERSON>n", "autoReady": "Tự động sẵn sàng", "completedAt": "<PERSON><PERSON><PERSON> thành lúc {time}", "@completedAt": {"description": "time", "placeholders": {"time": {"type": "String"}}}, "shareTableURLLink": "Chia sẻ đường dẫn Bàn", "shareQRCodeImage": "Chia sẻ hình ảnh mã QR", "orderTimeAtive": "Th<PERSON>i gian đặt {time}", "@orderTimeAtive": {"description": "time", "placeholders": {"time": {"type": "String"}}}, "orderName": "Đ<PERSON><PERSON> hàng:", "orderDate": "Ngày:", "range": "Khoảng:", "last7Days": "7 ngày qua", "last30Days": "30 ngày qua", "last3Months": "3 tháng qua", "last6Months": "6 tháng qua", "monthToDate": "<PERSON><PERSON> đầu tháng", "atRiskCustomers": "<PERSON><PERSON><PERSON><PERSON> hàng có nguy cơ rời bỏ", "championCustomers": "<PERSON><PERSON><PERSON><PERSON> hàng tiêu biểu", "hibernatingCustomers": "<PERSON><PERSON><PERSON><PERSON> hàng không hoạt động", "lostCustomers": "<PERSON><PERSON><PERSON><PERSON> hàng rời bỏ", "loyalCustomers": "<PERSON><PERSON><PERSON><PERSON> hàng thân thiết", "newCustomers": "<PERSON><PERSON><PERSON><PERSON> hàng mới", "potentialLoyalist": "<PERSON><PERSON><PERSON><PERSON> hàng tiềm năng", "reactivatedCustomers": "<PERSON><PERSON><PERSON><PERSON> hàng quay lại", "unlimitedUsage": "<PERSON><PERSON><PERSON>ng gi<PERSON>i hạn", "orderDelivery": "<PERSON><PERSON><PERSON>", "orderDeliveryDescription": "<PERSON><PERSON><PERSON><PERSON> hàng có thể chọn giao hàng khi thanh toán, hoàn tất thanh toán an toàn và theo dõi trạng thái đơn hàng của họ.", "orderDeliveryWarning": "<PERSON><PERSON><PERSON> c<PERSON> cổng thanh toán để giao hàng.", "delivery": "<PERSON><PERSON><PERSON>", "targetedOffers": "Ưu đãi cá nhân hoá", "duration": "<PERSON><PERSON><PERSON><PERSON> hạn", "added": "<PERSON><PERSON><PERSON>", "durationContent": "<PERSON><PERSON><PERSON> hạn sau {duration}", "@durationContent": {"description": "duration", "placeholders": {"duration": {"type": "String"}}}, "noResultsFound": "<PERSON><PERSON><PERSON><PERSON> tìm thấy kết quả nào.", "targetOffersEmpty": "<PERSON><PERSON><PERSON><PERSON> có dữ liệu để hiển thị ngay bây giờ!", "offerPerformance": "<PERSON><PERSON><PERSON> su<PERSON>t <PERSON>u đãi", "claimRate": "Tỷ lệ nhận", "redemptionRate": "Tỷ lệ đổi", "maxPerUserOffer": "<PERSON><PERSON> người dùng tối đa", "targetedOfferDetailTitle": "Ưu đãi cá nhân hoá", "rfmCustomerSegments": "Phân khúc khách hàng theo RFM", "rfmCustomerSegmentsSubtitle": "(<PERSON>ần đây - <PERSON><PERSON><PERSON><PERSON><PERSON> xuyên - <PERSON> tiêu)", "recency": "<PERSON><PERSON><PERSON><PERSON> gian", "frequency": "<PERSON><PERSON><PERSON>", "daysRecency": "<PERSON><PERSON><PERSON>", "@daysRecency": {"description": "s", "placeholders": {"s": {"type": "String"}}}, "atRiskCustomersSubTitle": "<PERSON><PERSON>ng là khách hàng thân thiết, nhưng gần đây chưa quay lại.", "championCustomersSubTitle": "<PERSON><PERSON><PERSON><PERSON> hàng gắn bó lâu dài và mang lại giá trị cao.", "hibernatingCustomersSubTitle": "<PERSON><PERSON> lâu không quay lại, c<PERSON> khả năng là khách hàng đã rời đi.", "lostCustomersSubTitle": "<PERSON><PERSON><PERSON><PERSON> còn ho<PERSON>t động, <PERSON><PERSON><PERSON><PERSON> xem là khách hàng đã rời đi.", "loyalCustomersSubTitle": "<PERSON><PERSON><PERSON><PERSON> hàng quay lại thư<PERSON>ng xuyên.", "newCustomersSubTitle": "<PERSON><PERSON><PERSON><PERSON> mới vừa tham gia.", "potentialLoyalistSubTitle": "<PERSON><PERSON><PERSON><PERSON> hàng mới, c<PERSON> tiềm năng trở thành khách thân thiết.", "reactivatedCustomersSubTitle": "<PERSON><PERSON><PERSON><PERSON> hàng vừa quay lại sau một thời gian không hoạt động.", "customerSegmentHistory": "<PERSON><PERSON><PERSON> sử phân khúc", "dashboard": "<PERSON><PERSON><PERSON> đi<PERSON> k<PERSON>n", "rfmCustomerSegmentsMenu": "Phân khúc khách hàng", "targetedOffer": "Ưu đãi cá nhân hoá", "segments": "Phân khúc", "sendingTime": "<PERSON><PERSON><PERSON><PERSON> gian g<PERSON>i", "firstOfferDay": "Ưu đãi đầu tiên - ngày", "secondOfferDay": "Ưu đãi thứ hai - ngày", "tooltipOfferFirst": "<PERSON><PERSON> một khách hàng vào phân kh<PERSON><PERSON> mới, tối đa 2 ưu đãi có thể được gửi – vào ngày đầu tiên và một ngày do bạn chọn (mặc định là ngày 15).", "tooltipOfferAvailable": "Ưu đãi đã sẵn sàng để gửi, nhưng sẽ không được gửi tự động trừ khi đã được lên lịch hoặc bật chế độ gửi tự động.", "claims": "<PERSON><PERSON><PERSON> c<PERSON>", "impressions": "<PERSON><PERSON><PERSON><PERSON> hi<PERSON>n thị", "segment": "Phân khúc", "estimatedCustomers": "<PERSON><PERSON><PERSON><PERSON> h<PERSON>", "estimatedSpending": "<PERSON> tiêu <PERSON> t<PERSON>h", "deliveryResults": "<PERSON><PERSON><PERSON> qu<PERSON> g<PERSON>", "inApp": "In-app", "upcomingIn24h": "<PERSON><PERSON><PERSON> diễn ra trong 24 giờ", "dailyUpcomingSends": "<PERSON><PERSON><PERSON><PERSON> g<PERSON>i sắp tới trong ngày ({segment})", "@dailyUpcomingSends": {"description": "segment", "placeholders": {"segment": {"type": "String"}}}, "upcomingEmpty": "<PERSON><PERSON><PERSON><PERSON> có ưu đãi nào đ<PERSON><PERSON><PERSON> lên lịch trong hôm nay.", "currentCustomerSegments": "<PERSON><PERSON> kh<PERSON>c khách hàng hiện tại", "smsWarning": "Kênh SMS không hoạt động do không đủ tín dụng", "unableToSendSMS": "<PERSON><PERSON><PERSON><PERSON> thể gửi SMS", "upcomingTOTitle": "<PERSON><PERSON><PERSON> ra", "channelPerformance": "<PERSON><PERSON><PERSON> k<PERSON>nh", "channels": "<PERSON><PERSON><PERSON>", "claimsChannels": "<PERSON><PERSON><PERSON><PERSON>", "allSegments": "<PERSON><PERSON><PERSON> c<PERSON>", "courierFound": "<PERSON><PERSON> tìm thấy giao hàng!", "estimatedArrival": "Ước t<PERSON>h đến lúc {time}", "@estimatedArrival": {"description": "time", "placeholders": {"time": {"type": "String"}}}, "courier": "<PERSON><PERSON><PERSON><PERSON> giao hàng", "assigningCourier": "<PERSON><PERSON><PERSON> ng<PERSON> giao hàng...", "onTheWay": "<PERSON><PERSON> giao", "deliveredOrder": "Đ<PERSON> giao hàng", "customerInformation": "Thông tin khách hàng", "deliveryDetails": "<PERSON> tiết giao hàng", "deliveryAddress": "Đ<PERSON>a chỉ giao hàng", "deliveryFee": "<PERSON><PERSON> giao hàng", "customerRefusedDelivery": "<PERSON><PERSON><PERSON><PERSON> từ chối nhận", "cannotFindCustomer": "<PERSON><PERSON><PERSON><PERSON> tìm đ<PERSON><PERSON> khách hàng", "deliveryTracking": "<PERSON> giao h<PERSON>ng", "failedOrder": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON>", "canceledOrder": "<PERSON><PERSON> h<PERSON>", "deliveryStatus": "<PERSON><PERSON><PERSON> trạng giao hàng", "chooseTicketFontSize": "<PERSON><PERSON><PERSON> k<PERSON>ch thư<PERSON><PERSON> chữ cho <PERSON>", "kitchenTicketSmall": "Nhỏ", "kitchenTicketDefault": "Mặc định", "kitchenTicketLarge": "Lớn", "receiptCustomerName": "<PERSON><PERSON><PERSON> h<PERSON>ng", "receiptPickupTime": "<PERSON><PERSON><PERSON><PERSON> gian nhận hàng", "receiptOrderNumber": "<PERSON><PERSON> đơn hàng", "receiptBusinessInfo": "<PERSON><PERSON><PERSON>ng tin cửa hàng", "receiptLogoPrinted": "Logo sẽ được chuyển đổi thành đen trắng khi in. <PERSON><PERSON> l<PERSON> in thử để kiểm tra chất lư<PERSON> in thực tế.", "receiptContact": "<PERSON>h<PERSON>ng tin liên hệ", "receiptOrderInfo": "<PERSON><PERSON><PERSON><PERSON> tin đơn hàng", "receiptAdditionalText": "<PERSON><PERSON><PERSON> b<PERSON><PERSON> b<PERSON> sung", "receiptCustomText": "<PERSON><PERSON><PERSON> chỉnh nội dung", "receiptCustomTextExample": "<PERSON><PERSON> dụ: \"Xin cảm ơn!\"", "noReceiptFound": "<PERSON><PERSON><PERSON><PERSON> tìm thấy phiếu in!", "printersReceipt": "Máy in & Phiếu In", "customizeReceipts": "<PERSON><PERSON><PERSON> chỉnh Phiếu In", "customizeKitchenTicket": "<PERSON><PERSON><PERSON>", "shippingFeeWillBeRefunded": "<PERSON><PERSON> vận chuyển sẽ được hoàn trả", "customerTargetedOffersIntegrationsSubtitle": "Ưu đãi cá nhân hóa dành riêng cho từng nhóm khách hàng nhằm tối đa hóa tương tác, thu hút khách hàng quay lại và tăng doanh thu.", "feeOff": "GIẢM", "feeFlatRate": "<PERSON><PERSON><PERSON> cố định", "feeFreeDelivery": "Giao hàng MIỄN PHÍ", "deliveryPoweredByUberDirect": "<PERSON><PERSON><PERSON> hàng đượ<PERSON> hỗ trợ bởi Uber Direct", "deliveryPoweredByUberDirectSub": "<PERSON><PERSON>g tôi đã hợp tác với Uber Direct để cung cấp dịch vụ giao hàng liền mạch cho khách hàng của bạn.", "deliverySettings": "Cài đặt giao hàng", "unitedStatesUberPricing": "Bảng giá Uber tại Hoa Kỳ", "pricingUberInCalifornia": "*G<PERSON>á tại California tăng thêm +$2 để phù hợp với <PERSON> luật Prop-22.", "miles": "dặm", "averageDelivery": "Thời gian giao hàng trung bình: 20-35 phút", "uberHandlesAllDispatching": "<PERSON><PERSON><PERSON>ng cần tip tài xế – Uber sẽ xử lý việc điều phối", "customersReceiveRealTimeTrackingLinks": "<PERSON> dõi trực tiếp: <PERSON><PERSON><PERSON><PERSON> hàng sẽ nhận được liên kết theo dõi theo thời gian thực", "youCanOfferFreeDiscountedDelivery": "Bạn có thể cung cấp giao hàng miễn phí hoặc giảm giá cho khách hàng bằng cách thiết lập quy tắc trong Ứng dụng StickyQR Manager (ví dụ: giao hàng miễn phí cho đơn hàng trên $150).", "deliveryFeeSupport": "Hỗ trợ phí giao hàng", "deliveryFeeSupportSub": "Ưu đãi giao hàng miễn phí hoặc giảm giá dựa trên tổng phụ (sau giảm giá): Tự động giảm $X hoặc miễn phí giao hàng khi tổng đơn hàng vượt quá $Y. Chọn mức phí cố định nếu áp dụng (Ví dụ: Tổng đơn hàng tối thiểu $30 → phí giao hàng là $6.99)", "subtotalMinium": "Tổng đơn hàng tối thiểu ($)", "addAnotherTier": "<PERSON><PERSON><PERSON><PERSON> mức hỗ trợ khác", "lastChange": "<PERSON>hay đổi gần đây", "sevenDays": "7 Ngày", "fourWeeks": "4 Tuần", "segmentedCustomers": "<PERSON><PERSON><PERSON><PERSON> hàng đã phân khúc", "totalTransactions": "Tổng số giao dịch", "newTransactionsIn24Hours": "<PERSON><PERSON>o d<PERSON>ch mới trong 24 giờ", "totalTransactionsChart": "Tổng số giao dịch {data}", "@totalTransactionsChart": {"description": "data", "placeholders": {"data": {"type": "String"}}}, "labels": "<PERSON><PERSON><PERSON><PERSON> dán", "receipt": "<PERSON><PERSON><PERSON>", "receipts": "<PERSON><PERSON><PERSON>", "printerDPI": "DPI máy in", "paperSize": "<PERSON><PERSON><PERSON>", "lastWeek": "<PERSON><PERSON><PERSON> tr<PERSON>", "lastMonth": "<PERSON><PERSON><PERSON><PERSON>", "printerStatus": "<PERSON><PERSON><PERSON><PERSON> thái m<PERSON> in", "receiptCopiesPerOrder": "<PERSON><PERSON> bản in hóa đơn mỗi đơn hàng", "kitchenTicket": "<PERSON><PERSON><PERSON> b<PERSON>", "categoriesToPrint": "<PERSON><PERSON> mục để in", "automaticallyPrint": "Tự động in đơn đang thực hiện", "allCategories": "<PERSON><PERSON><PERSON> bộ danh mục", "noPrinterConnected": "<PERSON><PERSON><PERSON><PERSON> có máy in <PERSON><PERSON><PERSON><PERSON> kết nối, vui lòng thiết lập máy in", "here": "tại đây", "hardware": "Hỗ trợ phần cứng", "showModifiers": "<PERSON><PERSON><PERSON> thị tùy chọn", "both": "Cả 2", "printAll": "In tất cả", "kitchenTicketOnly": "Chỉ in phiếu bếp", "receiptOnly": "Chỉ in hóa đơn", "options": "<PERSON><PERSON><PERSON>", "invoice": "<PERSON><PERSON> đ<PERSON>n", "receiptPickup": "<PERSON><PERSON><PERSON><PERSON> hàng", "confirmedBySystem": "<PERSON><PERSON><PERSON> nhận bởi hệ thống", "kitchenStaff": "Nhân viên", "orderFrom": "Đặt hàng từ", "chooseSpecificCategories": "<PERSON><PERSON><PERSON> các danh mục cụ thể để in. <PERSON><PERSON> trống sẽ là bao gồm tất cả.", "selectNone": "Bỏ chọn tất cả", "printerHelpStep33": "Tắt nguồn → ấn giữ nút \"Feed\" → bật nguồn trong khi vẫn giữ nút \"Feed\" → Máy sẽ in ra 2 tờ giấy → địa chỉ IP sẽ nằm trong tờ số 2", "printerHelpStep34": "Tắt nguồn → bật nguồn → mở nắp bỏ giấy in sau đó đóng lại → máy sẽ in ra địa chỉ IP", "caseOne": "Trườ<PERSON> hợp 1: ", "caseTwo": "<PERSON><PERSON>ư<PERSON><PERSON> hợ<PERSON> 2: ", "activity": "<PERSON><PERSON><PERSON> đ<PERSON>", "dateAndTime": "Ngày & giờ", "markInProgressDispatchDriver": "& <PERSON><PERSON><PERSON><PERSON> phối tài xế", "courierIsHeadingToThePickup": "<PERSON>à<PERSON> xế đã nhận đơn và đang đến nhà hàng.", "courierIsArrivingSoonPleasePrepareTheOrder": "<PERSON><PERSON><PERSON> x<PERSON> sắp đến <PERSON> hàng, xin hãy chuẩn bị hàng.", "courierIsHeadingYourWay": "Tài xế đang trên đường đến điểm giao hàng.", "courierIsPreparingForDropoff": "<PERSON><PERSON><PERSON> chuẩn bị giao hàng.", "latestArrivalBy": "<PERSON><PERSON><PERSON> nh<PERSON>t lúc {time}", "@latestArrivalBy": {"description": "time", "placeholders": {"time": {"type": "String"}}}, "deliveredTodayAt": "<PERSON><PERSON> giao hàng hôm nay lúc {time}", "@deliveredTodayAt": {"description": "time", "placeholders": {"time": {"type": "String"}}}, "justNow": "<PERSON><PERSON><PERSON> xong", "minuteAgo": "ph<PERSON><PERSON> tr<PERSON>", "@minuteAgo": {"description": "minute ago", "placeholders": {"s": {"type": "String"}}}, "hourAgo": "giờ trước", "@hourAgo": {"description": "hour ago", "placeholders": {"s": {"type": "String"}}}, "weekAgo": "tu<PERSON><PERSON> tr<PERSON><PERSON>", "@weekAgo": {"description": "week ago", "placeholders": {"s": {"type": "String"}}}, "dayAgo": "<PERSON><PERSON><PERSON> tr<PERSON>", "@dayAgo": {"description": "day ago", "placeholders": {"s": {"type": "String"}}}, "monthAgo": "th<PERSON><PERSON> t<PERSON>", "@monthAgo": {"description": "month ago", "placeholders": {"s": {"type": "String"}}}, "yearAgo": "năm tr<PERSON>", "@yearAgo": {"description": "year ago", "placeholders": {"s": {"type": "String"}}}, "noMessagesYet": "<PERSON><PERSON><PERSON> có tin nhắn nào", "joinedTheConversation": "đã tham gia cuộc trò chuy<PERSON>n", "leftTheConversation": "đ<PERSON> rời cuộc trò chuy<PERSON>n", "copy": "Sao chép", "newDelivery": "<PERSON><PERSON><PERSON> h<PERSON> mới", "enterName": "<PERSON><PERSON><PERSON><PERSON> tên", "addNewDelivery": "<PERSON><PERSON>o đơn giao mới", "selectDeliveryDateTime": "<PERSON><PERSON><PERSON> & g<PERSON><PERSON> giao", "selectDeliveryDate": "<PERSON><PERSON><PERSON> ng<PERSON>y giao hàng", "reviewQuote": "<PERSON><PERSON> b<PERSON>o giá", "pickupDetails": "<PERSON> tiết l<PERSON>y hàng", "scheduleAhead": "Đặt trước", "locationName": "Địa chỉ", "chooseATime": "<PERSON><PERSON><PERSON> thời gian", "addPickupLocationNotes": "Thêm ghi chú điểm lấy hàng (VD: số nhà, tên đường...)", "dropoffDetails": "<PERSON> tiết giao hàng", "addDeliveryNotes": "<PERSON>hê<PERSON> ghi chú giao hà<PERSON> (VD: số nhà, tên đường...)", "dropOffOptions": "<PERSON><PERSON><PERSON> ch<PERSON>n giao hàng", "handDelivery": "<PERSON><PERSON><PERSON> tận tay", "leaveAtDoor": "<PERSON><PERSON> tr<PERSON><PERSON><PERSON> c<PERSON>a", "avgDeliveryTime20to35mins": "Thời gian giao trung bình: 20-35 phút", "avgDeliveryTime": "Th<PERSON>i gian giao trung bình {time}", "@avgDeliveryTime": {"description": "time", "placeholders": {"time": {"type": "String"}}}, "deliverBy": "<PERSON><PERSON><PERSON> hàng bằng", "totalFare": "Tổng c<PERSON> phí", "selectDeliveryTime": "<PERSON><PERSON><PERSON> thời gian giao", "thisQuoteExpiresAt": "<PERSON><PERSON><PERSON> gi<PERSON> hết hạn lúc {time}", "@thisQuoteExpiresAt": {"description": "time", "placeholders": {"time": {"type": "String"}}}, "addTip": "<PERSON>hê<PERSON> tiền tip", "enterDeliveryAddress": "<PERSON><PERSON><PERSON><PERSON> địa chỉ giao hàng", "newDeliveryCustom": "K<PERSON><PERSON><PERSON>", "deliveryCustomerName": "<PERSON><PERSON><PERSON> h<PERSON>ng", "deliveryAddressRequired": "Địa chỉ giao hàng là bắt buộc", "deliveryTimeRequired": "Thời gian giao hàng phải ít nhất là 90 phút so với thời điểm hiện tại", "cancelDelivery": "Huỷ giao hàng", "cancelDeliveryMsg": "Bạn có chắc chắn muốn hủy đơn giao hàng này không?", "onlineActive": "<PERSON><PERSON><PERSON><PERSON>", "paymentSummary": "<PERSON><PERSON><PERSON> tắt thanh toán", "focusedHomeScreen": "<PERSON><PERSON><PERSON> hình tập trung", "experienceFocusedHomeScreen": "Tr<PERSON>i nghiệm màn hình chính tập trung ngay! Tối ưu thao tác, chuyển đổi liền mạch và quản lý dễ dàng!", "youCanSwitch": "Bạn có thể chuyển về màn hình chính linh hoạt bất cứ lúc nào trong Menu", "tryFocusedHome": "<PERSON><PERSON><PERSON><PERSON> nghiệm màn hình tập trung", "switchToFlexibleHome": "<PERSON><PERSON><PERSON><PERSON> sang màn hình ch<PERSON>h linh hoạt", "switchToFlexibleHomeMsg": "Bạn có chắc bạn muốn chuyển sang màn hình linh hoạt không?", "switchToFocusedHome": "<PERSON><PERSON><PERSON><PERSON> sang màn hình chính tập trung", "switchToFocusedHomeMsg": "Bạn có chắc chắn muốn thử màn hình chính tập trung không?", "tableStatus": "<PERSON><PERSON><PERSON><PERSON> thái bàn", "timeAtTableSinceFirstOrderRequest": "Thời gian ngồi tại bàn kể từ khi đặt hàng hoặc gửi yêu cầu", "enableTableStatus": "<PERSON><PERSON><PERSON> trạng thái bàn", "allowsYouToSetTablesToAutomaticallyChangeColorBasedOnHowLongGuestsHaveBeenSeated": "<PERSON> phép cài đặt màu sắc tự động thay đổi cho các bàn dựa trên thời gian khách hàng đã ngồi, gi<PERSON><PERSON> nhân viên dễ dàng nhận biết trạng thái của bàn.", "justSeated": "<PERSON><PERSON><PERSON> ng<PERSON> (Mặc định)", "statusColor": "<PERSON><PERSON><PERSON> s<PERSON>c trạng thái", "changeAfter": "<PERSON>hay đổi sau", "attentionRequired": "<PERSON><PERSON><PERSON> ch<PERSON>"}