name: stickyqrbusiness
description: StickyQR - Manager
version: 6.0.0+6000
publish_to: none

environment:
  sdk: ">=3.0.0 <4.0.0"

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter

  # Flutter offical plugins
  # connectivity_plus: ^6.1.3
  connectivity_plus: ^5.0.2
  device_info_plus: ^11.1.0
  # intl: ^0.20.2
  image_picker: ^1.1.2
  package_info_plus: ^8.3.0
  url_launcher: ^6.3.1

  # Firebase
  firebase_core: ^3.6.0
  firebase_messaging: ^15.1.3
  firebase_analytics: ^11.3.3
  firebase_crashlytics: ^4.1.3
  ably_flutter: ^1.2.37

  # State
  bloc: ^8.1.4
  flutter_bloc: ^8.1.6

  # Storage
  shared_preferences: ^2.5.3

  # JSON
  string_validator: ^1.1.0
  json_annotation: ^4.9.0

  # Utilities
  shimmer: ^3.0.0
  timezone: ^0.10.0
  dio: ^5.7.0
  flutter_svg: ^2.0.10+1
  flutter_easyloading: ^3.0.5
  flash: ^3.1.1
  infinite_scroll_pagination: ^5.0.0
  expandable: ^5.0.1
  flutter_swipe_action_cell: ^3.1.5
  # money2: any
  # money2: ^4.1.0
  money2: ^5.2.1
  formz: ^0.8.0
  flutter_sticky_header: ^0.7.0
  flutter_date_pickers: ^0.4.3
  cached_network_image: ^3.4.1
  simple_animations: ^5.0.2
  expandable_page_view: ^1.0.17
  carousel_slider: ^5.0.0
  scrollable_positioned_list: any
  flutter_rating_bar: ^4.0.1
  qr_flutter: ^4.1.0
  lottie: ^3.3.1
  icons_launcher: ^3.0.0
  # objectbox: ^4.0.3
  pinput: ^5.0.0
  mobile_scanner: ^6.0.2
  equatable: ^2.0.5
  # flutter_zebra_sdk: ^0.0.4+1
  flutter_zebra_sdk:
    path: ./local_packages/flutter_zebra_sdk
  flutter_staggered_grid_view: ^0.7.0
  phone_numbers_parser: ^9.0.1
  flutter_screen_lock: ^9.0.1
  reorderables: ^0.6.0
  wakelock_plus: ^1.2.11
  flutter_timezone: ^4.1.0
  # flutter_timezone: ^3.0.1
  intl_phone_number_input: ^0.7.4
  # calendar_date_picker2: ^1.1.7
  upgrader: ^11.3.1
  animated_flip_counter: ^0.3.4
  google_maps_flutter: ^2.12.1
  google_maps_flutter_ios: ^2.15.0
  geocoding: ^3.0.0
  geolocator: ^13.0.4
  flutter_libphonenumber: ^2.3.3
  image_cropper: ^9.1.0
  dotted_border: ^2.1.0
  printing: ^5.13.4
  image: ^4.5.4
  google_fonts: ^6.2.1
  network_info_plus: ^6.1.3
  flutter_inappwebview: ^6.1.5
  fl_chart: ^1.0.0
  # fl_chart: ^0.70.2
  tutorial_coach_mark: ^1.2.13
  flutter_cached_pdfview: ^0.4.3
  credit_card_type_detector: ^3.0.0
  flutter_markdown: ^0.7.3+2
  states_rebuilder: ^6.4.0
  flutter_image_compress: ^2.3.0
  sentry_flutter: ^8.14.1
  file_picker: ^9.0.2
  share_plus: ^10.1.1
  # quill_html_editor: ^2.2.8
  html2md: ^1.3.2
  markdown: ^7.3.0
  permission_handler: ^12.0.1
  # flutter_local_network_ios: ^0.0.1
  flutter_local_network_ios:
    path: ./local_packages/flutter_local_network_ios
  audioplayers: ^6.4.0
  drag_and_drop_lists: ^0.4.1
  table_calendar: ^3.2.0
  showcaseview: ^4.0.1
  esc_pos_utils_plus: ^2.0.4
  # flutter_esc_pos_network: ^1.0.3
  esc_pos_printer_lts: ^4.1.2
  esc_pos_utils_lts: any
  charset_converter: ^2.0.2
  diacritic: ^0.1.5
  responsive_framework: ^1.5.1
  flutter_html: ^3.0.0
  path_provider: ^2.1.5
  webview_flutter: ^4.13.0
  # flutter_star_prnt: ^2.4.1

dependency_overrides:
  intl: ^0.20.2

dev_dependencies:
  flutter_launcher_icons: ^0.14.3
  bloc_test: ^9.1.7
  flutter_test:
    sdk: flutter
  mocktail: ^1.0.4
  very_good_analysis: ^7.0.0
  json_serializable: ^6.8.0
  build_runner: ^2.4.13
  flutter_lints: ^5.0.0

flutter:
  uses-material-design: true
  generate: true
  assets:
    - assets/svgs/
    - assets/svgs/announcements/
    - assets/svgs/cb-template/
    - assets/svgs/signage-rewards/
    - assets/lotties/
    - assets/icon/
    - assets/theme-background/
    - assets/svgs/feature-tour/
    - assets/svgs/first-screen/
    - assets/sounds/beep.mp3
    - assets/sounds/tink.mp3
    - assets/sounds/pop.mp3
    - assets/svgs/call-button/
    - assets/svgs/call-button/all/
    - assets/svgs/call-button/icons.json
  fonts:
    - family: Inter
      fonts:
        - asset: assets/fonts/inter/Inter-Regular.ttf
        - asset: assets/fonts/inter/Inter-Italic.ttf
          style: italic
    - family: NotoEmoji
      fonts:
        - asset: assets/fonts/emoji/emoji.ttf
          weight: 400
    - family: Judson
      fonts:
        - asset: assets/fonts/judson/Judson-Bold.ttf
        - asset: assets/fonts/judson/Judson-Italic.ttf
        - asset: assets/fonts/judson/Judson-Regular.ttf
    - family: Montserrat
      fonts:
        - asset: assets/fonts/montserrat/Montserrat-ExtraBold.ttf
        - asset: assets/fonts/montserrat/Montserrat-Medium.ttf
    - family: Mali
      fonts:
        - asset: assets/fonts/mali/Mali-Bold.ttf
        - asset: assets/fonts/mali/Mali-Medium.ttf
    - family: InterBold
      fonts:
        - asset: assets/fonts/inter/Inter-SemiBold.ttf
    - family: Sansita
      fonts:
        - asset: assets/fonts/sansita/Sansita-Bold.ttf
    - family: Bellota
      fonts:
        - asset: assets/fonts/bellota/Bellota-Bold.ttf
        - asset: assets/fonts/bellota/Bellota-Italic.ttf
    - family: SVN-Androgyne
      fonts:
        - asset: assets/fonts/androgyne/SVN-Androgyne.ttf
    - family: SpaceGrotesk-Medium
      fonts:
        - asset: assets/fonts/space-grotesk/SpaceGrotesk-Medium.ttf
    - family: SansitaSwashed-Bold
      fonts:
        - asset: assets/fonts/sansita-swashed/SansitaSwashed-Bold.ttf
    - family: PlayfairDisplay-Bold
      fonts:
        - asset: assets/fonts/playfair-display-bold/PlayfairDisplay-Bold.ttf
    - family: FamiljenGrotesk
      fonts:
        - asset: assets/fonts/familjen-grotesk/FamiljenGrotesk-Regular.ttf
    - family: PlaypenBold
      fonts:
        - asset: assets/fonts/playpen/PlaypenSans-Bold.ttf
    - family: PlaypenSemiBold
      fonts:
        - asset: assets/fonts/playpen/PlaypenSans-SemiBold.ttf
    - family: PlaypenRegular
      fonts:
        - asset: assets/fonts/playpen/PlaypenSans-Regular.ttf
